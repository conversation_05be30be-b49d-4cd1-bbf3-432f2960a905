class DynamicAssignmentManager {
  async handleRouteDeviation(truck, actualLocation, expectedLocation) {
    // Create new assignment based on actual location
    const newAssignment = await this.createAdaptedAssignment(truck, actualLocation);
    
    // Mark original assignment as superseded
    await this.updateOriginalAssignment(truck.current_assignment_id);
    
    // Create audit record (not approval request)
    await this.createRouteChangeRecord(truck, actualLocation, expectedLocation);
    
    // Send notification to admin (informational only)
    this.notifyRouteChange(truck, actualLocation, expectedLocation);
    
    return newAssignment;
  }
}