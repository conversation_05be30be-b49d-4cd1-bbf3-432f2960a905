-- Migration: Dynamic Assignment Adaptation Integration
-- Date: 2025-06-27
-- Purpose: Add dynamic assignment adaptation fields to existing tables and ensure compatibility

-- ============================================================================
-- ASSIGNMENTS TABLE ENHANCEMENTS
-- ============================================================================

-- Add dynamic assignment adaptation fields to assignments table
DO $$ 
BEGIN 
    -- Add is_adaptive column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assignments' AND column_name = 'is_adaptive') THEN
        ALTER TABLE assignments ADD COLUMN is_adaptive BOOLEAN DEFAULT false;
        
        -- Set default value for existing records
        UPDATE assignments SET is_adaptive = false WHERE is_adaptive IS NULL;
        
        RAISE NOTICE 'Added is_adaptive column to assignments table';
    END IF;
    
    -- Add adaptation_strategy column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assignments' AND column_name = 'adaptation_strategy') THEN
        ALTER TABLE assignments ADD COLUMN adaptation_strategy VARCHAR(50);
        
        RAISE NOTICE 'Added adaptation_strategy column to assignments table';
    END IF;
    
    -- Add adaptation_confidence column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assignments' AND column_name = 'adaptation_confidence') THEN
        ALTER TABLE assignments ADD COLUMN adaptation_confidence VARCHAR(20);
        
        RAISE NOTICE 'Added adaptation_confidence column to assignments table';
    END IF;
    
    -- Add adaptation_metadata column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'assignments' AND column_name = 'adaptation_metadata') THEN
        ALTER TABLE assignments ADD COLUMN adaptation_metadata JSONB;
        
        RAISE NOTICE 'Added adaptation_metadata column to assignments table';
    END IF;
END $$;

-- ============================================================================
-- APPROVALS TABLE ENHANCEMENTS
-- ============================================================================

-- Add dynamic assignment adaptation fields to approvals table
DO $$ 
BEGIN 
    -- Add is_adaptive_exception column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'is_adaptive_exception') THEN
        ALTER TABLE approvals ADD COLUMN is_adaptive_exception BOOLEAN DEFAULT false;
        
        -- Set default value for existing records
        UPDATE approvals SET is_adaptive_exception = false WHERE is_adaptive_exception IS NULL;
        
        RAISE NOTICE 'Added is_adaptive_exception column to approvals table';
    END IF;
    
    -- Add adaptation_strategy column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'adaptation_strategy') THEN
        ALTER TABLE approvals ADD COLUMN adaptation_strategy VARCHAR(50);
        
        RAISE NOTICE 'Added adaptation_strategy column to approvals table';
    END IF;
    
    -- Add adaptation_confidence column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'adaptation_confidence') THEN
        ALTER TABLE approvals ADD COLUMN adaptation_confidence VARCHAR(20);
        
        RAISE NOTICE 'Added adaptation_confidence column to approvals table';
    END IF;
    
    -- Add auto_approved column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'auto_approved') THEN
        ALTER TABLE approvals ADD COLUMN auto_approved BOOLEAN DEFAULT false;
        
        -- Set default value for existing records
        UPDATE approvals SET auto_approved = false WHERE auto_approved IS NULL;
        
        RAISE NOTICE 'Added auto_approved column to approvals table';
    END IF;
    
    -- Add adaptation_metadata column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'adaptation_metadata') THEN
        ALTER TABLE approvals ADD COLUMN adaptation_metadata JSONB;
        
        RAISE NOTICE 'Added adaptation_metadata column to approvals table';
    END IF;
    
    -- Add suggested_assignment_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'approvals' AND column_name = 'suggested_assignment_id') THEN
        ALTER TABLE approvals ADD COLUMN suggested_assignment_id INTEGER REFERENCES assignments(id);
        
        RAISE NOTICE 'Added suggested_assignment_id column to approvals table';
    END IF;
END $$;

-- ============================================================================
-- PERFORMANCE INDEXES
-- ============================================================================

-- Create indexes for dynamic assignment adaptation fields (if they don't exist)
DO $$ 
BEGIN 
    -- Assignments table indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_assignments_adaptive') THEN
        CREATE INDEX idx_assignments_adaptive ON assignments(is_adaptive) WHERE is_adaptive = true;
        RAISE NOTICE 'Created index idx_assignments_adaptive';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_assignments_adaptation_strategy') THEN
        CREATE INDEX idx_assignments_adaptation_strategy ON assignments(adaptation_strategy) WHERE adaptation_strategy IS NOT NULL;
        RAISE NOTICE 'Created index idx_assignments_adaptation_strategy';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_assignments_adaptation_confidence') THEN
        CREATE INDEX idx_assignments_adaptation_confidence ON assignments(adaptation_confidence) WHERE adaptation_confidence IS NOT NULL;
        RAISE NOTICE 'Created index idx_assignments_adaptation_confidence';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_assignments_adaptive_status') THEN
        CREATE INDEX idx_assignments_adaptive_status ON assignments(is_adaptive, status, created_at) WHERE is_adaptive = true;
        RAISE NOTICE 'Created index idx_assignments_adaptive_status';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_assignments_adaptation_metadata_gin') THEN
        CREATE INDEX idx_assignments_adaptation_metadata_gin ON assignments USING gin(adaptation_metadata) WHERE adaptation_metadata IS NOT NULL;
        RAISE NOTICE 'Created index idx_assignments_adaptation_metadata_gin';
    END IF;
    
    -- Approvals table indexes
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_approvals_adaptive') THEN
        CREATE INDEX idx_approvals_adaptive ON approvals(is_adaptive_exception) WHERE is_adaptive_exception = true;
        RAISE NOTICE 'Created index idx_approvals_adaptive';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_approvals_adaptation_strategy') THEN
        CREATE INDEX idx_approvals_adaptation_strategy ON approvals(adaptation_strategy) WHERE adaptation_strategy IS NOT NULL;
        RAISE NOTICE 'Created index idx_approvals_adaptation_strategy';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_approvals_adaptation_confidence') THEN
        CREATE INDEX idx_approvals_adaptation_confidence ON approvals(adaptation_confidence) WHERE adaptation_confidence IS NOT NULL;
        RAISE NOTICE 'Created index idx_approvals_adaptation_confidence';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_approvals_auto_approved') THEN
        CREATE INDEX idx_approvals_auto_approved ON approvals(auto_approved) WHERE auto_approved = true;
        RAISE NOTICE 'Created index idx_approvals_auto_approved';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_approvals_suggested_assignment') THEN
        CREATE INDEX idx_approvals_suggested_assignment ON approvals(suggested_assignment_id) WHERE suggested_assignment_id IS NOT NULL;
        RAISE NOTICE 'Created index idx_approvals_suggested_assignment';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_approvals_adaptive_metadata_gin') THEN
        CREATE INDEX idx_approvals_adaptive_metadata_gin ON approvals USING gin(adaptation_metadata) WHERE adaptation_metadata IS NOT NULL;
        RAISE NOTICE 'Created index idx_approvals_adaptive_metadata_gin';
    END IF;
END $$;

-- ============================================================================
-- DATA INTEGRITY CONSTRAINTS
-- ============================================================================

-- Add constraints for adaptive assignment fields
DO $$ 
BEGIN 
    -- Constraint for adaptation_strategy values
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                   WHERE constraint_name = 'chk_assignments_adaptation_strategy') THEN
        ALTER TABLE assignments ADD CONSTRAINT chk_assignments_adaptation_strategy 
        CHECK (adaptation_strategy IS NULL OR adaptation_strategy IN ('pattern_based', 'proximity_based', 'efficiency_based', 'manual_override'));
        RAISE NOTICE 'Added constraint chk_assignments_adaptation_strategy';
    END IF;
    
    -- Constraint for adaptation_confidence values
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                   WHERE constraint_name = 'chk_assignments_adaptation_confidence') THEN
        ALTER TABLE assignments ADD CONSTRAINT chk_assignments_adaptation_confidence 
        CHECK (adaptation_confidence IS NULL OR adaptation_confidence IN ('high', 'medium', 'low'));
        RAISE NOTICE 'Added constraint chk_assignments_adaptation_confidence';
    END IF;
    
    -- Constraint for approvals adaptation_strategy values
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                   WHERE constraint_name = 'chk_approvals_adaptation_strategy') THEN
        ALTER TABLE approvals ADD CONSTRAINT chk_approvals_adaptation_strategy 
        CHECK (adaptation_strategy IS NULL OR adaptation_strategy IN ('pattern_based', 'proximity_based', 'efficiency_based', 'manual_override'));
        RAISE NOTICE 'Added constraint chk_approvals_adaptation_strategy';
    END IF;
    
    -- Constraint for approvals adaptation_confidence values
    IF NOT EXISTS (SELECT 1 FROM information_schema.check_constraints 
                   WHERE constraint_name = 'chk_approvals_adaptation_confidence') THEN
        ALTER TABLE approvals ADD CONSTRAINT chk_approvals_adaptation_confidence 
        CHECK (adaptation_confidence IS NULL OR adaptation_confidence IN ('high', 'medium', 'low'));
        RAISE NOTICE 'Added constraint chk_approvals_adaptation_confidence';
    END IF;
END $$;

-- ============================================================================
-- ANALYTICS VIEW FOR DYNAMIC ASSIGNMENTS
-- ============================================================================

-- Create view for dynamic assignment analytics
CREATE OR REPLACE VIEW v_dynamic_assignment_analytics AS
SELECT 
    -- Overall statistics
    COUNT(*) as total_adaptive_assignments,
    COUNT(CASE WHEN status = 'assigned' THEN 1 END) as active_adaptive_assignments,
    COUNT(CASE WHEN status = 'pending_approval' THEN 1 END) as pending_adaptive_assignments,
    
    -- Strategy breakdown
    COUNT(CASE WHEN adaptation_strategy = 'pattern_based' THEN 1 END) as pattern_based_count,
    COUNT(CASE WHEN adaptation_strategy = 'proximity_based' THEN 1 END) as proximity_based_count,
    COUNT(CASE WHEN adaptation_strategy = 'efficiency_based' THEN 1 END) as efficiency_based_count,
    COUNT(CASE WHEN adaptation_strategy = 'manual_override' THEN 1 END) as manual_override_count,
    
    -- Confidence breakdown
    COUNT(CASE WHEN adaptation_confidence = 'high' THEN 1 END) as high_confidence_count,
    COUNT(CASE WHEN adaptation_confidence = 'medium' THEN 1 END) as medium_confidence_count,
    COUNT(CASE WHEN adaptation_confidence = 'low' THEN 1 END) as low_confidence_count,
    
    -- Performance metrics
    ROUND(AVG(CASE WHEN created_at >= CURRENT_DATE - INTERVAL '7 days' THEN 1 ELSE 0 END), 2) as weekly_creation_rate,
    
    -- Success rate (assignments that became active)
    ROUND(
        COUNT(CASE WHEN status = 'assigned' THEN 1 END)::numeric / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as success_rate_percent,
    
    -- Last updated
    CURRENT_TIMESTAMP as last_updated
FROM assignments 
WHERE is_adaptive = true;

-- ============================================================================
-- COMPLETION MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE 'Dynamic Assignment Adaptation Integration Migration - COMPLETED SUCCESSFULLY';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE '';
    RAISE NOTICE 'Enhancements Applied:';
    RAISE NOTICE '  ✅ assignments table - Added adaptive assignment tracking fields';
    RAISE NOTICE '  ✅ approvals table - Added adaptive exception handling fields';
    RAISE NOTICE '  ✅ Performance indexes - Created for optimal query performance';
    RAISE NOTICE '  ✅ Data integrity constraints - Added validation rules';
    RAISE NOTICE '  ✅ Analytics view - Created for dynamic assignment monitoring';
    RAISE NOTICE '';
    RAISE NOTICE 'New Features Enabled:';
    RAISE NOTICE '  🚀 Hybrid exception management with dynamic adaptation';
    RAISE NOTICE '  🚀 Confidence-based auto-approval system';
    RAISE NOTICE '  🚀 Pattern analysis integration with exception flow';
    RAISE NOTICE '  🚀 Comprehensive adaptation analytics and monitoring';
    RAISE NOTICE '';
    RAISE NOTICE 'System is ready for Dynamic Assignment Adaptation!';
    RAISE NOTICE '============================================================================';
    RAISE NOTICE '';
END $$;
