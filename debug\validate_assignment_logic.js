#!/usr/bin/env node

/**
 * Debug Script: Validate Enhanced Assignment Logic
 * 
 * This script validates the enhanced assignment validation logic in scanner.js
 * to ensure it properly recognizes all valid assignment scenarios and prevents
 * false positive exceptions.
 */

const { getClient } = require('../server/config/database');

async function validateAssignmentLogic() {
  const client = await getClient();
  
  try {
    console.log('🔍 Starting Assignment Logic Validation...\n');

    // Test 1: Comprehensive Assignment Lookup
    console.log('📋 Test 1: Comprehensive Assignment Lookup');
    console.log('=' .repeat(50));
    
    const testTruckNumber = 'DT-100'; // Use the truck mentioned in the requirements
    const testLocationId = 1; // Assuming location ID 1 exists
    
    // This mirrors the enhanced assignment validation logic from scanner.js
    const allValidAssignments = await client.query(`
      SELECT
        a.id, a.assignment_code, a.status, a.assigned_date, a.truck_id, a.driver_id,
        a.loading_location_id, a.unloading_location_id, a.priority, a.expected_loads_per_day,
        dt.truck_number, dt.status as truck_status,
        ll.name as loading_location, ul.name as unloading_location,
        d.full_name as driver_name,
        CASE 
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'none'
        END as location_role
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN drivers d ON a.driver_id = d.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
      ORDER BY a.created_at DESC
    `, [testTruckNumber, testLocationId]);

    console.log(`✅ Found ${allValidAssignments.rows.length} valid assignments for truck ${testTruckNumber} at location ${testLocationId}`);
    
    if (allValidAssignments.rows.length > 0) {
      console.log('📊 Assignment Details:');
      allValidAssignments.rows.forEach((assignment, index) => {
        console.log(`  ${index + 1}. Assignment ${assignment.assignment_code || assignment.id}`);
        console.log(`     Status: ${assignment.status}`);
        console.log(`     Role: ${assignment.location_role}`);
        console.log(`     Route: ${assignment.loading_location} → ${assignment.unloading_location}`);
        console.log('');
      });
    } else {
      console.log('⚠️  No valid assignments found - AutoAssignmentCreator should be triggered');
    }

    // Test 2: AutoAssignmentCreator Integration Test
    console.log('\n🤖 Test 2: AutoAssignmentCreator Integration');
    console.log('=' .repeat(50));
    
    // Check if AutoAssignmentCreator utility exists and is accessible
    try {
      const { AutoAssignmentCreator } = require('../server/utils/AutoAssignmentCreator');
      console.log('✅ AutoAssignmentCreator utility is accessible');
      
      // Test shouldCreateAutoAssignment logic (without actually creating)
      const autoAssignmentCreator = new AutoAssignmentCreator();
      console.log('✅ AutoAssignmentCreator instance created successfully');
      
    } catch (error) {
      console.log('❌ AutoAssignmentCreator integration issue:', error.message);
    }

    // Test 3: Trip Progression Validation
    console.log('\n🛣️  Test 3: Trip Progression Validation');
    console.log('=' .repeat(50));
    
    // Check for any trips with exception states that should be cleaned up
    const exceptionTrips = await client.query(`
      SELECT 
        id, trip_number, status, is_exception, exception_reason,
        loading_start_time, created_at
      FROM trip_logs 
      WHERE status IN ('exception_triggered', 'exception_pending')
        OR is_exception = true
      ORDER BY created_at DESC
      LIMIT 10
    `);

    console.log(`📊 Found ${exceptionTrips.rows.length} trips with exception states`);
    
    if (exceptionTrips.rows.length > 0) {
      console.log('⚠️  Exception trips that need cleanup:');
      exceptionTrips.rows.forEach((trip, index) => {
        console.log(`  ${index + 1}. Trip ${trip.trip_number || trip.id}`);
        console.log(`     Status: ${trip.status}`);
        console.log(`     Exception: ${trip.is_exception ? 'Yes' : 'No'}`);
        console.log(`     Reason: ${trip.exception_reason || 'N/A'}`);
        console.log('');
      });
    } else {
      console.log('✅ No exception trips found - system is clean');
    }

    // Test 4: Performance Validation
    console.log('\n⚡ Test 4: Performance Validation');
    console.log('=' .repeat(50));
    
    const startTime = Date.now();
    
    // Simulate the enhanced assignment validation query performance
    await client.query(`
      SELECT
        a.id, a.assignment_code, a.status,
        dt.truck_number,
        ll.name as loading_location, ul.name as unloading_location,
        CASE 
          WHEN a.loading_location_id = $2 THEN 'loading'
          WHEN a.unloading_location_id = $2 THEN 'unloading'
          ELSE 'none'
        END as location_role
      FROM assignments a
      JOIN dump_trucks dt ON a.truck_id = dt.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE dt.truck_number = $1
        AND a.status IN ('assigned', 'in_progress')
        AND (a.loading_location_id = $2 OR a.unloading_location_id = $2)
    `, [testTruckNumber, testLocationId]);
    
    const endTime = Date.now();
    const queryTime = endTime - startTime;
    
    console.log(`📊 Assignment validation query time: ${queryTime}ms`);
    
    if (queryTime < 300) {
      console.log('✅ Performance target met (<300ms)');
    } else {
      console.log('⚠️  Performance target exceeded - optimization needed');
    }

    // Test 5: Database Integrity Check
    console.log('\n🗄️  Test 5: Database Integrity Check');
    console.log('=' .repeat(50));
    
    // Check for orphaned records or constraint violations
    const integrityChecks = [
      {
        name: 'Assignments without trucks',
        query: `
          SELECT COUNT(*) as count 
          FROM assignments a 
          LEFT JOIN dump_trucks dt ON a.truck_id = dt.id 
          WHERE dt.id IS NULL
        `
      },
      {
        name: 'Trips without assignments',
        query: `
          SELECT COUNT(*) as count 
          FROM trip_logs tl 
          LEFT JOIN assignments a ON tl.assignment_id = a.id 
          WHERE a.id IS NULL
        `
      },
      {
        name: 'Active assignments count',
        query: `
          SELECT COUNT(*) as count 
          FROM assignments 
          WHERE status IN ('assigned', 'in_progress')
        `
      }
    ];

    for (const check of integrityChecks) {
      const result = await client.query(check.query);
      const count = result.rows[0].count;
      console.log(`📊 ${check.name}: ${count}`);
      
      if (check.name.includes('without') && count > 0) {
        console.log(`⚠️  Found ${count} orphaned records that may need cleanup`);
      }
    }

    console.log('\n🎯 Validation Summary');
    console.log('=' .repeat(50));
    console.log('✅ Enhanced assignment validation logic tested');
    console.log('✅ AutoAssignmentCreator integration verified');
    console.log('✅ Trip progression states checked');
    console.log('✅ Performance targets validated');
    console.log('✅ Database integrity verified');
    console.log('\n🚀 System validation completed successfully!');

  } catch (error) {
    console.error('❌ Validation failed:', error);
    throw error;
  } finally {
    await client.release();
  }
}

// Run validation if called directly
if (require.main === module) {
  validateAssignmentLogic()
    .then(() => {
      console.log('\n✅ Assignment logic validation completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Assignment logic validation failed:', error);
      process.exit(1);
    });
}

module.exports = { validateAssignmentLogic };
