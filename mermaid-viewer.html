<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Trip System Flowchart</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            background-color: #1e1e1e;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }
        .container {
            max-width: 100%;
            overflow: auto;
            margin-bottom: 20px;
        }
        .instructions {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            max-width: 800px;
        }
        h1 {
            color: #4caf50;
        }
        ol {
            line-height: 1.6;
        }
        #diagram {
            width: 100%;
            max-width: 1800px;
            margin: 0 auto;
        }
        .mermaid {
            background-color: #1e1e1e;
        }
    </style>
</head>
<body>
    <h1>QR Trip System Flowchart</h1>
    
    <div class="instructions">
        <h2>How to Save as Image:</h2>
        <ol>
            <li>Wait for the diagram to fully render</li>
            <li>Right-click on the diagram</li>
            <li>Select "Save image as..." from the context menu</li>
            <li>Choose a location and filename</li>
            <li>Click Save</li>
        </ol>
        <p>Alternatively, you can take a screenshot of the diagram (using PrtScn key or Windows+Shift+S).</p>
    </div>
    
    <div id="diagram" class="container">
        <div class="mermaid">
flowchart TD
    A["📱 QR Scanner App"] --> B{"Scan Type?"}
    B -->|Location| C["📍 Location Scan"]
    B -->|Truck| D["🚛 Truck Scan"]
    
    C --> E["Validate Location QR"]
    E -->|Valid| F["Store Location Data"]
    E -->|Invalid| G["❌ Invalid QR Error"]
    
    F --> H["✅ Location Stored - Scan Truck Next"]
    
    D --> I["Validate Truck QR"]
    I -->|Valid| J["🔍 Enhanced Assignment Validation"]
    I -->|Invalid| K["❌ Invalid QR Error"]
    
    J --> L{"Assignment Found?"}
    
    L -->|Yes| M["✅ Valid Assignment Found"]
    L -->|No| N["🤖 AutoAssignmentCreator Check"]
    
    M --> O{"Active Trip Exists?"}
    
    O -->|Yes| P["📊 Trip Progression Logic"]
    O -->|No| Q["🆕 Create New Trip"]
    
    N --> R{"Should Create Assignment?"}
    
    R -->|Yes| S["🔧 Create Auto-Assignment"]
    R -->|No| T["❌ Assignment Creation Error"]
    
    S --> U["✅ Assignment Created - Status: assigned"]
    
    U --> Q
    
    P --> V{"Current Status?"}
    
    V -->|loading_start| W["📦 Complete Loading"]
    V -->|loading_end| X["🚛 Start Travel"]
    V -->|unloading_start| Y["📤 Complete Unloading"]
    V -->|unloading_end| Z["✅ Trip Completed"]
    
    Q --> AA["🎯 Determine Initial Action"]
    
    AA --> BB{"Location Role?"}
    
    BB -->|Loading| CC["📦 Start Loading"]
    BB -->|Unloading| DD["📤 Start Unloading"]
    
    CC --> EE["Status: loading_start"]
    DD --> FF["Status: unloading_start"]
    
    W --> GG["Status: loading_end"]
    X --> HH["Status: travel"]
    Y --> II["Status: unloading_end"]
    Z --> JJ["Status: trip_completed"]
    
    EE --> KK["📡 WebSocket Notification"]
    FF --> KK
    GG --> KK
    HH --> KK
    II --> KK
    JJ --> KK
    
    KK --> LL["📊 Update Dashboard"]
    LL --> MM["✅ Response to Frontend"]
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'dark',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });
    </script>
</body>
</html>