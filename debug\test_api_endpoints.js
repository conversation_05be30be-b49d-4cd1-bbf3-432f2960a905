#!/usr/bin/env node

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPIEndpoints() {
  console.log('🔗 Testing API Endpoints...\n');

  const baseOptions = {
    hostname: 'localhost',
    port: 5000,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E'
    }
  };

  const tests = [
    {
      name: 'Assignments API - Get All',
      options: { ...baseOptions, path: '/api/assignments', method: 'GET' }
    },
    {
      name: 'Analytics API - Dashboard',
      options: { ...baseOptions, path: '/api/analytics/dashboard', method: 'GET' }
    },
    {
      name: 'Analytics API - Assignments (New)',
      options: { ...baseOptions, path: '/api/analytics/assignments', method: 'GET' }
    },
    {
      name: 'Trucks API - Get All',
      options: { ...baseOptions, path: '/api/trucks', method: 'GET' }
    }
  ];

  for (const test of tests) {
    try {
      console.log(`🔍 Testing: ${test.name}`);
      const startTime = Date.now();
      const response = await makeRequest(test.options, test.data);
      const endTime = Date.now();
      const duration = endTime - startTime;

      if (response.statusCode === 200) {
        console.log(`   ✅ Status: ${response.statusCode} (${duration}ms)`);
        if (response.body && typeof response.body === 'object') {
          if (Array.isArray(response.body.data)) {
            console.log(`   📊 Data: ${response.body.data.length} records`);
          } else if (response.body.data) {
            console.log(`   📊 Data: Object with ${Object.keys(response.body.data).length} properties`);
          }
        }
      } else {
        console.log(`   ❌ Status: ${response.statusCode} (${duration}ms)`);
        console.log(`   📋 Error: ${response.body.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`   ❌ Request failed: ${error.message}`);
    }
    console.log('');
  }

  // Test the removed exception endpoints (should return 404)
  console.log('🚫 Testing Removed Exception Endpoints (should fail):');
  
  const removedEndpoints = [
    '/api/analytics/exceptions',
    '/api/approvals'
  ];

  for (const endpoint of removedEndpoints) {
    try {
      console.log(`🔍 Testing: ${endpoint}`);
      const response = await makeRequest({ ...baseOptions, path: endpoint, method: 'GET' });
      
      if (response.statusCode === 404) {
        console.log(`   ✅ Correctly removed (404)`);
      } else {
        console.log(`   ⚠️  Unexpected status: ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`   ✅ Correctly removed (connection refused)`);
    }
    console.log('');
  }

  console.log('🎯 API endpoint testing completed');
}

if (require.main === module) {
  testAPIEndpoints()
    .then(() => {
      console.log('\n✅ API endpoint tests completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ API endpoint tests failed:', error);
      process.exit(1);
    });
}

module.exports = { testAPIEndpoints };
