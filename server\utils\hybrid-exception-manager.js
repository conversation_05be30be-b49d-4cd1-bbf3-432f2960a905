/**
 * Hybrid Exception Manager
 * 
 * This module integrates the traditional Exception Management system with
 * the new Dynamic Assignment Adaptation system, providing a seamless
 * hybrid approach that preserves existing functionality while adding
 * intelligent automation capabilities.
 * 
 * Key Features:
 * - Confidence-based exception handling
 * - Automatic approval for high-confidence adaptations
 * - Seamless integration with existing approval workflow
 * - Enhanced WebSocket notifications for different scenarios
 * - Comprehensive audit trail for all decisions
 */

const { getClient } = require('../config/database');
const { logger } = require('./logger');
const { 
  createExceptionAndSetPending, 
  processApprovalAndUpdateTrip,
  EXCEPTION_STATES 
} = require('./exception-flow-manager');
const { 
  dynamicAssignmentAdapter, 
  ADAPTATION_STRATEGIES, 
  CONFIDENCE_LEVELS 
} = require('./dynamic-assignment-adapter');
const { notifyExceptionCreated, notifyExceptionUpdated } = require('../websocket');

/**
 * Hybrid Exception Types
 */
const HYBRID_EXCEPTION_TYPES = {
  TRADITIONAL: 'traditional',           // Standard manual exception
  ADAPTIVE_AUTO: 'adaptive_auto',       // Auto-approved adaptive exception
  ADAPTIVE_REVIEW: 'adaptive_review',   // Adaptive exception requiring review
  ADAPTIVE_MANUAL: 'adaptive_manual'    // Manual override with adaptation data
};

/**
 * Hybrid Exception Manager Class
 */
class HybridExceptionManager {
  constructor() {
    this.logger = logger;
    this.adapter = dynamicAssignmentAdapter;
  }

  /**
   * Create hybrid exception with dynamic adaptation analysis
   * @param {Object} params - Exception parameters
   * @returns {Promise<Object>} Exception creation result
   */
  async createHybridException(params) {
    const {
      tripLogId,
      truckNumber,
      currentLocationId,
      exceptionType,
      exceptionDescription,
      severity = 'medium',
      reportedBy,
      originalAssignment = null
    } = params;

    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Perform dynamic adaptation analysis
      let adaptationAnalysis = null;
      let hybridType = HYBRID_EXCEPTION_TYPES.TRADITIONAL;
      let autoApprove = false;

      try {
        adaptationAnalysis = await this.adapter.analyzeMovementPatterns(
          truckNumber,
          currentLocationId,
          { client }
        );

        // Determine hybrid exception type based on analysis
        const result = this._determineHybridType(adaptationAnalysis, severity);
        hybridType = result.type;
        autoApprove = result.autoApprove;

        this.logger.info('Adaptation analysis completed for exception', {
          trip_log_id: tripLogId,
          truck_number: truckNumber,
          hybrid_type: hybridType,
          auto_approve: autoApprove,
          suggestions_count: adaptationAnalysis.suggestions.length
        });

      } catch (adaptationError) {
        this.logger.warn('Adaptation analysis failed, falling back to traditional exception', {
          trip_log_id: tripLogId,
          truck_number: truckNumber,
          error: adaptationError.message
        });
        // Fall back to traditional exception handling
      }

      // Create the exception with adaptation metadata
      const exceptionData = {
        exception_type: exceptionType,
        exception_description: exceptionDescription,
        severity,
        reported_by: reportedBy,
        original_status: 'loading_start'
      };

      // Create base exception
      const exceptionResult = await createExceptionAndSetPending(client, tripLogId, exceptionData);

      // Get the created approval ID
      const approvalResult = await client.query(`
        SELECT id FROM approvals 
        WHERE trip_log_id = $1 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [tripLogId]);

      if (approvalResult.rows.length === 0) {
        throw new Error('Failed to retrieve created approval');
      }

      const approvalId = approvalResult.rows[0].id;

      // Update approval with adaptation metadata
      await this._updateApprovalWithAdaptationData(
        client, 
        approvalId, 
        adaptationAnalysis, 
        hybridType, 
        autoApprove
      );

      // Handle auto-approval for high-confidence adaptations
      if (autoApprove && hybridType === HYBRID_EXCEPTION_TYPES.ADAPTIVE_AUTO) {
        await this._processAutoApproval(client, approvalId, adaptationAnalysis, reportedBy);
      }

      await client.query('COMMIT');

      // Send appropriate notifications
      await this._sendHybridNotifications(
        approvalId, 
        hybridType, 
        autoApprove, 
        exceptionData, 
        adaptationAnalysis
      );

      return {
        success: true,
        approvalId,
        hybridType,
        autoApproved: autoApprove,
        adaptationAnalysis,
        message: autoApprove 
          ? 'Exception auto-approved based on high-confidence pattern analysis'
          : 'Exception created and pending review with adaptation insights'
      };

    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Hybrid exception creation failed', {
        trip_log_id: tripLogId,
        truck_number: truckNumber,
        error: error.message
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Process approval with hybrid intelligence
   * @param {number} approvalId - Approval ID
   * @param {string} decision - approved/rejected
   * @param {number} reviewedBy - User ID
   * @param {string} notes - Review notes
   * @returns {Promise<Object>} Processing result
   */
  async processHybridApproval(approvalId, decision, reviewedBy, notes = '') {
    const client = await getClient();

    try {
      await client.query('BEGIN');

      // Get approval with adaptation data
      const approvalResult = await client.query(`
        SELECT a.*, tl.assignment_id, tl.notes as trip_notes
        FROM approvals a
        JOIN trip_logs tl ON a.trip_log_id = tl.id
        WHERE a.id = $1
      `, [approvalId]);

      if (approvalResult.rows.length === 0) {
        throw new Error(`Approval ${approvalId} not found`);
      }

      const approval = approvalResult.rows[0];

      // If this is an adaptive exception and being approved, create adaptive assignment
      if (decision === 'approved' && approval.is_adaptive_exception && approval.adaptation_metadata) {
        await this._createAdaptiveAssignmentFromApproval(client, approval);
      }

      // Process the approval using existing flow manager
      const result = await processApprovalAndUpdateTrip(client, approvalId, decision, reviewedBy, notes);

      await client.query('COMMIT');

      // Send enhanced notifications
      await this._sendApprovalNotifications(approval, decision, result);

      return {
        ...result,
        isAdaptive: approval.is_adaptive_exception,
        adaptationStrategy: approval.adaptation_strategy,
        adaptationConfidence: approval.adaptation_confidence
      };

    } catch (error) {
      await client.query('ROLLBACK');
      this.logger.error('Hybrid approval processing failed', {
        approval_id: approvalId,
        decision,
        error: error.message
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Get hybrid exception analytics
   * @param {Object} filters - Analytics filters
   * @returns {Promise<Object>} Analytics data
   */
  async getHybridAnalytics(filters = {}) {
    const client = await getClient();

    try {
      const { startDate, endDate, hybridType, strategy } = filters;

      let whereClause = 'WHERE 1=1';
      const params = [];
      let paramCount = 0;

      if (startDate) {
        whereClause += ` AND a.created_at >= $${++paramCount}`;
        params.push(startDate);
      }

      if (endDate) {
        whereClause += ` AND a.created_at <= $${++paramCount}`;
        params.push(endDate);
      }

      if (hybridType) {
        whereClause += ` AND a.adaptation_metadata->>'hybrid_type' = $${++paramCount}`;
        params.push(hybridType);
      }

      if (strategy) {
        whereClause += ` AND a.adaptation_strategy = $${++paramCount}`;
        params.push(strategy);
      }

      const analyticsQuery = `
        SELECT 
          COUNT(*) as total_exceptions,
          COUNT(CASE WHEN a.is_adaptive_exception = true THEN 1 END) as adaptive_exceptions,
          COUNT(CASE WHEN a.auto_approved = true THEN 1 END) as auto_approved_exceptions,
          COUNT(CASE WHEN a.status = 'approved' THEN 1 END) as approved_exceptions,
          COUNT(CASE WHEN a.status = 'rejected' THEN 1 END) as rejected_exceptions,
          COUNT(CASE WHEN a.status = 'pending' THEN 1 END) as pending_exceptions,
          
          -- Strategy breakdown
          COUNT(CASE WHEN a.adaptation_strategy = 'pattern_based' THEN 1 END) as pattern_based_count,
          COUNT(CASE WHEN a.adaptation_strategy = 'proximity_based' THEN 1 END) as proximity_based_count,
          COUNT(CASE WHEN a.adaptation_strategy = 'efficiency_based' THEN 1 END) as efficiency_based_count,
          
          -- Confidence breakdown
          COUNT(CASE WHEN a.adaptation_confidence = 'high' THEN 1 END) as high_confidence_count,
          COUNT(CASE WHEN a.adaptation_confidence = 'medium' THEN 1 END) as medium_confidence_count,
          COUNT(CASE WHEN a.adaptation_confidence = 'low' THEN 1 END) as low_confidence_count,
          
          -- Performance metrics
          ROUND(AVG(CASE WHEN a.auto_approved = true THEN 1 ELSE 0 END) * 100, 2) as auto_approval_rate,
          ROUND(AVG(CASE WHEN a.status = 'approved' THEN 1 ELSE 0 END) * 100, 2) as approval_rate
        FROM approvals a
        ${whereClause}
      `;

      const result = await client.query(analyticsQuery, params);

      return {
        success: true,
        analytics: result.rows[0],
        filters,
        generatedAt: new Date().toISOString()
      };

    } catch (error) {
      this.logger.error('Hybrid analytics query failed', {
        filters,
        error: error.message
      });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Private helper methods
   */

  _determineHybridType(adaptationAnalysis, severity) {
    if (!adaptationAnalysis || !adaptationAnalysis.suggestions.length) {
      return { type: HYBRID_EXCEPTION_TYPES.TRADITIONAL, autoApprove: false };
    }

    const topSuggestion = adaptationAnalysis.suggestions[0];

    // High confidence + low/medium severity = auto-approve
    if (topSuggestion.confidence === CONFIDENCE_LEVELS.HIGH && 
        ['low', 'medium'].includes(severity)) {
      return { type: HYBRID_EXCEPTION_TYPES.ADAPTIVE_AUTO, autoApprove: true };
    }

    // Medium/low confidence or high severity = require review
    if (topSuggestion.confidence === CONFIDENCE_LEVELS.MEDIUM || 
        topSuggestion.confidence === CONFIDENCE_LEVELS.LOW ||
        severity === 'high' || severity === 'critical') {
      return { type: HYBRID_EXCEPTION_TYPES.ADAPTIVE_REVIEW, autoApprove: false };
    }

    return { type: HYBRID_EXCEPTION_TYPES.TRADITIONAL, autoApprove: false };
  }

  async _updateApprovalWithAdaptationData(client, approvalId, adaptationAnalysis, hybridType, autoApprove) {
    const adaptationMetadata = {
      hybrid_type: hybridType,
      auto_approved: autoApprove,
      analysis_timestamp: new Date().toISOString(),
      suggestions: adaptationAnalysis?.suggestions || [],
      analysis_summary: adaptationAnalysis?.analysis || {}
    };

    const topSuggestion = adaptationAnalysis?.suggestions?.[0];

    await client.query(`
      UPDATE approvals 
      SET is_adaptive_exception = true,
          adaptation_strategy = $1,
          adaptation_confidence = $2,
          auto_approved = $3,
          adaptation_metadata = $4,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $5
    `, [
      topSuggestion?.strategy || null,
      topSuggestion?.confidence || null,
      autoApprove,
      JSON.stringify(adaptationMetadata),
      approvalId
    ]);
  }

  async _processAutoApproval(client, approvalId, adaptationAnalysis, reportedBy) {
    // Auto-approve the exception
    await client.query(`
      UPDATE approvals 
      SET status = 'approved',
          reviewed_by = $1,
          reviewed_at = CURRENT_TIMESTAMP,
          notes = 'Auto-approved based on high-confidence pattern analysis',
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
    `, [reportedBy, approvalId]); // Use system or reported_by as reviewer for auto-approval

    this.logger.info('Exception auto-approved', {
      approval_id: approvalId,
      strategy: adaptationAnalysis.suggestions[0]?.strategy,
      confidence: adaptationAnalysis.suggestions[0]?.confidence
    });
  }

  async _createAdaptiveAssignmentFromApproval(client, approval) {
    // Extract assignment creation logic from adaptation metadata
    const metadata = JSON.parse(approval.adaptation_metadata || '{}');
    const suggestions = metadata.suggestions || [];

    if (suggestions.length > 0) {
      const topSuggestion = suggestions[0];
      
      // Create adaptive assignment based on suggestion
      // This would integrate with the existing assignment creation logic
      this.logger.info('Creating adaptive assignment from approval', {
        approval_id: approval.id,
        suggestion: topSuggestion
      });
    }
  }

  async _sendHybridNotifications(approvalId, hybridType, autoApproved, exceptionData, adaptationAnalysis) {
    try {
      if (autoApproved) {
        // Send auto-approval notification
        notifyExceptionUpdated({
          id: approvalId,
          exception_type: exceptionData.exception_type,
          exception_description: exceptionData.exception_description,
          severity: exceptionData.severity,
          auto_approved: true,
          adaptation_strategy: adaptationAnalysis.suggestions[0]?.strategy
        }, 'auto_approved');
      } else {
        // Send standard exception notification with adaptation insights
        notifyExceptionCreated({
          id: approvalId,
          exception_type: exceptionData.exception_type,
          exception_description: exceptionData.exception_description,
          severity: exceptionData.severity,
          is_adaptive: hybridType !== HYBRID_EXCEPTION_TYPES.TRADITIONAL,
          adaptation_insights: adaptationAnalysis?.suggestions?.length > 0
        });
      }
    } catch (notifyError) {
      this.logger.error('Failed to send hybrid notifications', {
        approval_id: approvalId,
        error: notifyError.message
      });
    }
  }

  async _sendApprovalNotifications(approval, decision, result) {
    try {
      notifyExceptionUpdated({
        id: approval.id,
        exception_type: approval.exception_type,
        exception_description: approval.exception_description,
        severity: approval.severity,
        is_adaptive: approval.is_adaptive_exception,
        adaptation_strategy: approval.adaptation_strategy
      }, decision);
    } catch (notifyError) {
      this.logger.error('Failed to send approval notifications', {
        approval_id: approval.id,
        error: notifyError.message
      });
    }
  }
}

// Export singleton instance
const hybridExceptionManager = new HybridExceptionManager();

module.exports = {
  HybridExceptionManager,
  hybridExceptionManager,
  HYBRID_EXCEPTION_TYPES
};
