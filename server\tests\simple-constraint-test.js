/**
 * Simple Exception Flow Transaction Test Script
 * 
 * This is a simplified version of the concurrency test that creates its own test data
 * and focuses specifically on testing the constraint violation fix.
 * 
 * <AUTHOR> QR Trip System Team
 * @version 1.0.0
 * @since 2025-07-01
 * 
 * Usage: node server/tests/simple-constraint-test.js
 */

const { getClient } = require('../config/database');
const exceptionFlowManager = require('../utils/exception-flow-manager');

// Test configuration constants
const TEST_CONFIG = {
  TRUCK: {
    number: 'TEST-TRUCK-001',
    capacity: 25,
    status: 'active',
    license: 'TEST001'
  },
  DRIVER: {
    name: 'Test Driver',
    license: 'TEST123456',
    status: 'active'
  },
  LOCATIONS: {
    loading: {
      name: 'Test Loading Site',
      type: 'loading',
      address: '123 Test Street',
      status: 'active'
    },
    unloading: {
      name: 'Test Unloading Site',
      type: 'unloading', 
      address: '456 Test Avenue',
      status: 'active'
    }
  },
  ASSIGNMENT: {
    status: 'assigned',
    priority: 'normal'
  },
  TEST_TRIP_COUNT: 3
};

// Logging utilities for consistent output
const logger = {
  header: (message) => {
    console.log('====================================');
    console.log(message);
    console.log('====================================');
  },
  
  step: (stepNumber, message) => {
    console.log(`\nSTEP ${stepNumber}: ${message}...`);
  },
  
  info: (message, data = null) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ℹ️ ${message}`);
    if (data) {
      console.log('  ', JSON.stringify(data, null, 2));
    }
  },
  
  success: (message, data = null) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ✅ ${message}`);
    if (data) {
      console.log('  ', JSON.stringify(data, null, 2));
    }
  },
  
  warning: (message, data = null) => {
    const timestamp = new Date().toISOString();
    console.warn(`[${timestamp}] ⚠️ ${message}`);
    if (data) {
      console.warn('  ', JSON.stringify(data, null, 2));
    }
  },
  
  error: (message, error = null) => {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] ❌ ${message}`);
    if (error) {
      console.error('  Error:', error.message);
      if (process.env.NODE_ENV === 'development') {
        console.error('  Stack:', error.stack);
      }
    }
  },
  
  summary: (title, stats) => {
    console.log(`\n📊 ${title}:`);
    Object.entries(stats).forEach(([key, value]) => {
      console.log(`  - ${key}: ${value}`);
    });
  }
};

/**
 * Test data creation utilities
 */
class TestDataFactory {
  constructor(client) {
    this.client = client;
    this.createdEntities = {
      trucks: [],
      drivers: [],
      locations: [],
      assignments: [],
      tripLogs: []
    };
  }

  /**
   * Create test truck with error handling
   * @returns {Promise<Object>} Created truck object
   */
  async createTestTruck() {
    try {
      const result = await this.client.query(`
        INSERT INTO dump_trucks (truck_number, capacity_tons, status, license_plate)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (truck_number) DO UPDATE SET
          capacity_tons = EXCLUDED.capacity_tons,
          status = EXCLUDED.status,
          license_plate = EXCLUDED.license_plate
        RETURNING id, truck_number, capacity_tons, status
      `, [
        TEST_CONFIG.TRUCK.number,
        TEST_CONFIG.TRUCK.capacity,
        TEST_CONFIG.TRUCK.status,
        TEST_CONFIG.TRUCK.license
      ]);

      const truck = result.rows[0];
      this.createdEntities.trucks.push(truck.id);
      
      logger.success(`Created test truck: ${truck.truck_number}`, {
        id: truck.id,
        capacity: truck.capacity_tons,
        status: truck.status
      });
      
      return truck;
    } catch (error) {
      logger.error('Failed to create test truck', error);
      throw new Error(`Test truck creation failed: ${error.message}`);
    }
  }

  /**
   * Create test driver with error handling
   * @returns {Promise<Object>} Created driver object
   */
  async createTestDriver() {
    try {
      const result = await this.client.query(`
        INSERT INTO drivers (full_name, license_number, status)
        VALUES ($1, $2, $3)
        ON CONFLICT (license_number) DO UPDATE SET
          full_name = EXCLUDED.full_name,
          status = EXCLUDED.status
        RETURNING id, full_name, license_number, status
      `, [
        TEST_CONFIG.DRIVER.name,
        TEST_CONFIG.DRIVER.license,
        TEST_CONFIG.DRIVER.status
      ]);

      const driver = result.rows[0];
      this.createdEntities.drivers.push(driver.id);
      
      logger.success(`Created test driver: ${driver.full_name}`, {
        id: driver.id,
        license: driver.license_number,
        status: driver.status
      });
      
      return driver;
    } catch (error) {
      logger.error('Failed to create test driver', error);
      throw new Error(`Test driver creation failed: ${error.message}`);
    }
  }

  /**
   * Create test locations with error handling
   * @returns {Promise<Object>} Object containing loading and unloading locations
   */
  async createTestLocations() {
    try {
      // Create loading location
      const loadingResult = await this.client.query(`
        INSERT INTO locations (name, type, address, status)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO UPDATE SET
          type = EXCLUDED.type,
          address = EXCLUDED.address,
          status = EXCLUDED.status
        RETURNING id, name, type, address, status
      `, [
        TEST_CONFIG.LOCATIONS.loading.name,
        TEST_CONFIG.LOCATIONS.loading.type,
        TEST_CONFIG.LOCATIONS.loading.address,
        TEST_CONFIG.LOCATIONS.loading.status
      ]);

      // Create unloading location
      const unloadingResult = await this.client.query(`
        INSERT INTO locations (name, type, address, status)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (name) DO UPDATE SET
          type = EXCLUDED.type,
          address = EXCLUDED.address,
          status = EXCLUDED.status
        RETURNING id, name, type, address, status
      `, [
        TEST_CONFIG.LOCATIONS.unloading.name,
        TEST_CONFIG.LOCATIONS.unloading.type,
        TEST_CONFIG.LOCATIONS.unloading.address,
        TEST_CONFIG.LOCATIONS.unloading.status
      ]);

      const locations = {
        loading: loadingResult.rows[0],
        unloading: unloadingResult.rows[0]
      };

      this.createdEntities.locations.push(locations.loading.id, locations.unloading.id);
      
      logger.success('Created test locations', {
        loading: { id: locations.loading.id, name: locations.loading.name },
        unloading: { id: locations.unloading.id, name: locations.unloading.name }
      });
      
      return locations;
    } catch (error) {
      logger.error('Failed to create test locations', error);
      throw new Error(`Test location creation failed: ${error.message}`);
    }
  }

  /**
   * Create test assignment with error handling
   * @param {Object} truck - Truck object
   * @param {Object} driver - Driver object
   * @param {Object} locations - Locations object
   * @param {string} suffix - Assignment code suffix
   * @returns {Promise<Object>} Created assignment object
   */
  async createTestAssignment(truck, driver, locations, suffix = '') {
    try {
      const assignmentCode = `TEST-${suffix}-${Date.now()}`;
      
      const result = await this.client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id,
          loading_location_id, unloading_location_id, 
          status, priority, assigned_date
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_DATE)
        RETURNING id, assignment_code, truck_id, driver_id, 
                  loading_location_id, unloading_location_id, status, priority
      `, [
        assignmentCode,
        truck.id,
        driver.id,
        locations.loading.id,
        locations.unloading.id,
        TEST_CONFIG.ASSIGNMENT.status,
        TEST_CONFIG.ASSIGNMENT.priority
      ]);

      const assignment = result.rows[0];
      this.createdEntities.assignments.push(assignment.id);
      
      logger.success(`Created test assignment: ${assignment.assignment_code}`, {
        id: assignment.id,
        truck_id: assignment.truck_id,
        driver_id: assignment.driver_id,
        status: assignment.status,
        priority: assignment.priority
      });
      
      return assignment;
    } catch (error) {
      logger.error('Failed to create test assignment', error);
      throw new Error(`Test assignment creation failed: ${error.message}`);
    }
  }

  /**
   * Create test trip log with error handling
   * @param {number} assignmentId - Assignment ID
   * @param {number} tripNumber - Trip number
   * @returns {Promise<Object>} Created trip log object
   */
  async createTestTripLog(assignmentId, tripNumber = 1) {
    try {
      const result = await this.client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, is_exception, created_at
        )
        VALUES ($1, $2, 'exception_pending', true, CURRENT_TIMESTAMP)
        RETURNING id, assignment_id, trip_number, status, is_exception, created_at
      `, [assignmentId, tripNumber]);

      const tripLog = result.rows[0];
      this.createdEntities.tripLogs.push(tripLog.id);
      
      logger.success(`Created test trip log: ${tripLog.id}`, {
        assignment_id: tripLog.assignment_id,
        trip_number: tripLog.trip_number,
        status: tripLog.status,
        is_exception: tripLog.is_exception
      });
      
      return tripLog;
    } catch (error) {
      logger.error('Failed to create test trip log', error);
      throw new Error(`Test trip log creation failed: ${error.message}`);
    }
  }

  /**
   * Clean up all created test data
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      logger.info('Starting test data cleanup...');
      
      // Delete in reverse order due to foreign key constraints
      if (this.createdEntities.tripLogs.length > 0) {
        await this.client.query(`
          DELETE FROM trip_logs WHERE id = ANY($1)
        `, [this.createdEntities.tripLogs]);
        logger.info(`Cleaned up ${this.createdEntities.tripLogs.length} trip logs`);
      }

      if (this.createdEntities.assignments.length > 0) {
        await this.client.query(`
          DELETE FROM assignments WHERE id = ANY($1)
        `, [this.createdEntities.assignments]);
        logger.info(`Cleaned up ${this.createdEntities.assignments.length} assignments`);
      }

      if (this.createdEntities.drivers.length > 0) {
        await this.client.query(`
          DELETE FROM drivers WHERE id = ANY($1)
        `, [this.createdEntities.drivers]);
        logger.info(`Cleaned up ${this.createdEntities.drivers.length} drivers`);
      }

      if (this.createdEntities.locations.length > 0) {
        await this.client.query(`
          DELETE FROM locations WHERE id = ANY($1)
        `, [this.createdEntities.locations]);
        logger.info(`Cleaned up ${this.createdEntities.locations.length} locations`);
      }

      if (this.createdEntities.trucks.length > 0) {
        await this.client.query(`
          DELETE FROM dump_trucks WHERE id = ANY($1)
        `, [this.createdEntities.trucks]);
        logger.info(`Cleaned up ${this.createdEntities.trucks.length} trucks`);
      }

      logger.success('Test data cleanup completed successfully');
      
    } catch (error) {
      logger.warning('Test data cleanup encountered issues', error);
      // Don't throw here as cleanup issues shouldn't fail the test
    }
  }
}
/**
 * Constraint Violation Test Suite
 */
class ConstraintViolationTestSuite {
  constructor() {
    this.client = null;
    this.dataFactory = null;
    this.testResults = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * Initialize test environment
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      this.client = await getClient();
      this.dataFactory = new TestDataFactory(this.client);
      logger.info('Test environment initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize test environment', error);
      throw error;
    }
  }

  /**
   * Clean up test environment
   * @returns {Promise<void>}
   */
  async cleanup() {
    try {
      if (this.dataFactory) {
        await this.dataFactory.cleanup();
      }
      if (this.client) {
        this.client.release();
      }
      logger.info('Test environment cleaned up successfully');
    } catch (error) {
      logger.warning('Test environment cleanup encountered issues', error);
    }
  }

  /**
   * Record test result
   * @param {boolean} passed - Whether test passed
   * @param {string} testName - Name of the test
   * @param {Error|null} error - Error if test failed
   */
  recordResult(passed, testName, error = null) {
    this.testResults.totalTests++;
    if (passed) {
      this.testResults.passed++;
      logger.success(`Test passed: ${testName}`);
    } else {
      this.testResults.failed++;
      this.testResults.errors.push({ testName, error: error?.message || 'Unknown error' });
      logger.error(`Test failed: ${testName}`, error);
    }
  }

  /**
   * Test feasibility check functionality
   * @param {Object} targetAssignment - Target assignment for reassignment
   * @param {Array} testTrips - Array of test trip objects
   * @returns {Promise<Object>} Test results
   */
  async testFeasibilityChecks(targetAssignment, testTrips) {
    logger.step('4A', 'Testing feasibility check functionality');
    
    const feasibilityResults = [];
    
    for (let i = 0; i < testTrips.length; i++) {
      const trip = testTrips[i];
      
      try {
        const feasibilityCheck = await exceptionFlowManager.verifyAssignmentUpdateFeasibility(
          this.client, trip.id, targetAssignment
        );
        
        feasibilityResults.push({
          tripId: trip.id,
          feasible: feasibilityCheck.feasible,
          requiresUpdate: feasibilityCheck.requiresUpdate,
          suggestedTripNumber: feasibilityCheck.suggestedTripNumber,
          hadConflict: feasibilityCheck.hadConflict,
          conflictResolutionStrategy: feasibilityCheck.conflictResolutionStrategy,
          reason: feasibilityCheck.reason
        });
        
        logger.info(`Trip ${trip.id} feasibility check completed`, {
          feasible: feasibilityCheck.feasible,
          requiresUpdate: feasibilityCheck.requiresUpdate,
          suggestedTripNumber: feasibilityCheck.suggestedTripNumber
        });
        
        this.recordResult(true, `Feasibility check for trip ${trip.id}`);
        
      } catch (error) {
        logger.error(`Feasibility check failed for trip ${trip.id}`, error);
        this.recordResult(false, `Feasibility check for trip ${trip.id}`, error);
        
        feasibilityResults.push({
          tripId: trip.id,
          feasible: false,
          error: error.message
        });
      }
    }
    
    return feasibilityResults;
  }

  /**
   * Test actual trip updates with constraint handling
   * @param {Object} targetAssignment - Target assignment for reassignment
   * @param {Array} testTrips - Array of test trip objects
   * @param {Array} feasibilityResults - Results from feasibility checks
   * @returns {Promise<Array>} Update results
   */
  async testTripUpdates(targetAssignment, testTrips, feasibilityResults) {
    logger.step('5A', 'Testing trip updates with constraint handling');
    
    const updateResults = [];
    
    for (let i = 0; i < testTrips.length; i++) {
      const trip = testTrips[i];
      const feasibility = feasibilityResults[i];
      
      if (!feasibility.feasible) {
        logger.warning(`Skipping trip ${trip.id} update - not feasible: ${feasibility.reason}`);
        updateResults.push({
          tripId: trip.id,
          success: false,
          skipped: true,
          reason: feasibility.reason
        });
        continue;
      }
      
      try {
        await this.client.query('BEGIN');
        
        logger.info(`Attempting to update trip ${trip.id}...`);
        
        // Re-verify feasibility within transaction for safety
        const freshFeasibilityCheck = await exceptionFlowManager.verifyAssignmentUpdateFeasibility(
          this.client, trip.id, targetAssignment
        );
        
        if (!freshFeasibilityCheck.feasible) {
          await this.client.query('ROLLBACK');
          logger.warning(`Fresh feasibility check failed for trip ${trip.id}: ${freshFeasibilityCheck.reason}`);
          updateResults.push({
            tripId: trip.id,
            success: false,
            reason: 'Fresh feasibility check failed'
          });
          this.recordResult(false, `Trip ${trip.id} update (fresh feasibility failed)`);
          continue;
        }
        
        // Perform the update
        await exceptionFlowManager.updateTripToUseExistingAssignment(
          this.client, trip.id, targetAssignment, 1,
          { suggestedTripNumber: freshFeasibilityCheck.suggestedTripNumber }
        );
        
        // Verify the update succeeded
        const verifyResult = await this.client.query(`
          SELECT assignment_id, trip_number, status FROM trip_logs WHERE id = $1
        `, [trip.id]);
        
        const updatedTrip = verifyResult.rows[0];
        
        if (updatedTrip.assignment_id === targetAssignment.id) {
          await this.client.query('COMMIT');
          
          logger.success(`Trip ${trip.id} updated successfully`, {
            assignment_id: updatedTrip.assignment_id,
            trip_number: updatedTrip.trip_number,
            status: updatedTrip.status
          });
          
          updateResults.push({
            tripId: trip.id,
            success: true,
            newTripNumber: updatedTrip.trip_number,
            newStatus: updatedTrip.status
          });
          
          this.recordResult(true, `Trip ${trip.id} update`);
        } else {
          await this.client.query('ROLLBACK');
          logger.error(`Trip ${trip.id} update verification failed - assignment ID mismatch`);
          updateResults.push({
            tripId: trip.id,
            success: false,
            reason: 'Assignment ID verification failed'
          });
          this.recordResult(false, `Trip ${trip.id} update (verification failed)`);
        }
        
      } catch (error) {
        await this.client.query('ROLLBACK');
        logger.error(`Trip ${trip.id} update failed`, error);
        updateResults.push({
          tripId: trip.id,
          success: false,
          error: error.message
        });
        this.recordResult(false, `Trip ${trip.id} update`, error);
      }
    }
    
    return updateResults;
  }

  /**
   * Verify constraint compliance after updates
   * @param {Object} targetAssignment - Target assignment to check
   * @returns {Promise<Object>} Constraint check results
   */
  async verifyConstraintCompliance(targetAssignment) {
    logger.step('6A', 'Verifying constraint compliance');
    
    try {
      // Check for duplicate (assignment_id, trip_number) combinations
      const constraintCheckResult = await this.client.query(`
        SELECT 
          assignment_id,
          trip_number,
          COUNT(*) as duplicate_count,
          array_agg(id) as trip_ids
        FROM trip_logs
        WHERE assignment_id = $1
        GROUP BY assignment_id, trip_number
        HAVING COUNT(*) > 1
      `, [targetAssignment.id]);
      
      const hasViolations = constraintCheckResult.rows.length > 0;
      
      if (hasViolations) {
        logger.error('CONSTRAINT VIOLATIONS DETECTED!');
        constraintCheckResult.rows.forEach(row => {
          logger.error(`Duplicate found`, {
            assignment_id: row.assignment_id,
            trip_number: row.trip_number,
            duplicate_count: row.duplicate_count,
            trip_ids: row.trip_ids
          });
        });
        this.recordResult(false, 'Constraint compliance check');
      } else {
        logger.success('NO CONSTRAINT VIOLATIONS - All trip numbers are unique per assignment');
        this.recordResult(true, 'Constraint compliance check');
      }
      
      // Get final trip distribution
      const distributionResult = await this.client.query(`
        SELECT assignment_id, trip_number, id as trip_id, status
        FROM trip_logs
        WHERE assignment_id = $1
        ORDER BY trip_number
      `, [targetAssignment.id]);
      
      logger.info('Final trip distribution on target assignment:');
      distributionResult.rows.forEach(row => {
        logger.info(`  Trip ${row.trip_id}: trip_number=${row.trip_number}, status=${row.status}`);
      });
      
      return {
        hasViolations,
        violations: constraintCheckResult.rows,
        distribution: distributionResult.rows
      };
      
    } catch (error) {
      logger.error('Constraint compliance verification failed', error);
      this.recordResult(false, 'Constraint compliance check', error);
      throw error;
    }
  }

  /**
   * Print final test summary
   */
  printSummary() {
    logger.header('TEST SUMMARY');
    
    logger.summary('RESULTS', {
      'Total Tests': this.testResults.totalTests,
      'Passed': this.testResults.passed,
      'Failed': this.testResults.failed,
      'Success Rate': `${((this.testResults.passed / this.testResults.totalTests) * 100).toFixed(1)}%`
    });
    
    if (this.testResults.failed > 0) {
      logger.error('FAILED TESTS:');
      this.testResults.errors.forEach((error, index) => {
        logger.error(`  ${index + 1}. ${error.testName}: ${error.error}`);
      });
    }
    
    const overallResult = this.testResults.failed === 0 ? 'PASSED' : 'FAILED';
    logger.header(`CONSTRAINT VIOLATION TEST ${overallResult}`);
  }
}

/**
 * Main test execution function
 * @returns {Promise<void>}
 */
async function runSimpleConstraintTest() {
  logger.header('SIMPLE CONSTRAINT VIOLATION TEST');
  logger.info('Testing the database constraint fix with minimal setup');
  
  const testSuite = new ConstraintViolationTestSuite();
  
  try {
    // Initialize test environment
    await testSuite.initialize();
    
    // Step 1: Create test data
    logger.step(1, 'Creating minimal test data');
    
    const truck = await testSuite.dataFactory.createTestTruck();
    const driver = await testSuite.dataFactory.createTestDriver();
    const locations = await testSuite.dataFactory.createTestLocations();

    // Step 2: Create target assignment for reassignment
    logger.step(2, 'Creating target assignment');
    const targetAssignment = await testSuite.dataFactory.createTestAssignment(
      truck, driver, locations, 'TARGET'
    );

    // Step 3: Create source assignments and trip logs
    logger.step(3, 'Creating source assignments and trip logs');
    const testTrips = [];
    
    for (let i = 0; i < TEST_CONFIG.TEST_TRIP_COUNT; i++) {
      const sourceAssignment = await testSuite.dataFactory.createTestAssignment(
        truck, driver, locations, `SOURCE-${i}`
      );
      
      const tripLog = await testSuite.dataFactory.createTestTripLog(sourceAssignment.id);
      
      testTrips.push({
        id: tripLog.id,
        sourceAssignmentId: sourceAssignment.id,
        sourceAssignmentCode: sourceAssignment.assignment_code
      });
    }

    // Step 4: Test feasibility checks
    const feasibilityResults = await testSuite.testFeasibilityChecks(targetAssignment, testTrips);

    // Step 5: Test actual updates
    const updateResults = await testSuite.testTripUpdates(targetAssignment, testTrips, feasibilityResults);

    // Step 6: Verify constraint compliance
    const constraintResults = await testSuite.verifyConstraintCompliance(targetAssignment);

    // Generate summary
    const successfulUpdates = updateResults.filter(r => r.success).length;
    logger.summary('TEST EXECUTION SUMMARY', {
      'Test Trips Created': testTrips.length,
      'Feasibility Checks': feasibilityResults.length,
      'Successful Updates': successfulUpdates,
      'Constraint Violations': constraintResults.hasViolations ? 'DETECTED' : 'NONE',
      'Overall Status': constraintResults.hasViolations ? 'FAILED' : 'PASSED'
    });

  } catch (error) {
    logger.error('Test execution failed', error);
    testSuite.recordResult(false, 'Overall test execution', error);
  } finally {
    // Cleanup and print results
    await testSuite.cleanup();
    testSuite.printSummary();
  }
}
  
  try {
    // Step 1: Create minimal test data
    console.log('STEP 1: Creating minimal test data...');
    
    // Create a test truck
    const truckResult = await client.query(`
      INSERT INTO dump_trucks (truck_number, capacity_tons, status, license_plate)
      VALUES ('TEST-TRUCK-001', 25, 'active', 'TEST001')
      RETURNING id, truck_number
    `);
    const testTruck = truckResult.rows[0];
    console.log(`Created test truck: ${testTruck.truck_number} (ID: ${testTruck.id})`);
    
    // Create a test driver
    const driverResult = await client.query(`
      INSERT INTO drivers (full_name, license_number, status)
      VALUES ('Test Driver', 'TEST123456', 'active')
      RETURNING id, full_name
    `);
    const testDriver = driverResult.rows[0];
    console.log(`Created test driver: ${testDriver.full_name} (ID: ${testDriver.id})`);
    
    // Create test locations
    const loadingLocationResult = await client.query(`
      INSERT INTO locations (name, type, address, status)
      VALUES ('Test Loading Site', 'loading', '123 Test Street', 'active')
      RETURNING id, name
    `);
    const loadingLocation = loadingLocationResult.rows[0];
    
    const unloadingLocationResult = await client.query(`
      INSERT INTO locations (name, type, address, status)
      VALUES ('Test Unloading Site', 'unloading', '456 Test Avenue', 'active')
      RETURNING id, name
    `);
    const unloadingLocation = unloadingLocationResult.rows[0];
    
    console.log(`Created test locations: ${loadingLocation.name}, ${unloadingLocation.name}`);

    // Step 2: Create a target assignment for reassignment
    console.log('\nSTEP 2: Creating target assignment...');
    const targetAssignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id, 
        status, priority, assigned_date
      )
      VALUES ($1, $2, $3, $4, $5, 'assigned', 'normal', CURRENT_DATE)
      RETURNING id, assignment_code
    `, [
      'TEST-TARGET-' + Date.now(),
      testTruck.id,
      testDriver.id,
      loadingLocation.id,
      unloadingLocation.id
    ]);
    
    const targetAssignment = targetAssignmentResult.rows[0];
    console.log(`Created target assignment: ${targetAssignment.assignment_code} (ID: ${targetAssignment.id})`);

    // Step 3: Create multiple source assignments and trip logs
    console.log('\nSTEP 3: Creating source assignments and trip logs...');
    const testTrips = [];
    
    for (let i = 0; i < 3; i++) {
      // Create source assignment
      const sourceAssignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id,
          loading_location_id, unloading_location_id, 
          status, priority, assigned_date
        )
        VALUES ($1, $2, $3, $4, $5, 'assigned', 'normal', CURRENT_DATE)
        RETURNING id, assignment_code
      `, [
        `TEST-SOURCE-${i}-${Date.now()}`,
        testTruck.id,
        testDriver.id,
        loadingLocation.id,
        unloadingLocation.id
      ]);
      
      const sourceAssignment = sourceAssignmentResult.rows[0];
      
      // Create trip log on source assignment
      const tripResult = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, is_exception, created_at
        )
        VALUES ($1, 1, 'exception_pending', true, CURRENT_TIMESTAMP)
        RETURNING id
      `, [sourceAssignment.id]);
      
      testTrips.push({
        id: tripResult.rows[0].id,
        sourceAssignmentId: sourceAssignment.id,
        sourceAssignmentCode: sourceAssignment.assignment_code
      });
      
      console.log(`Created trip ${i+1}: ID ${tripResult.rows[0].id} on assignment ${sourceAssignment.assignment_code}`);
    }

    // Step 4: Test constraint handling
    console.log('\nSTEP 4: Testing constraint violation handling...');
    
    console.log('Testing feasibility checks...');
    for (let i = 0; i < testTrips.length; i++) {
      const trip = testTrips[i];
      const feasibilityCheck = await exceptionFlowManager.verifyAssignmentUpdateFeasibility(
        client, trip.id, targetAssignment
      );
      
      console.log(`Trip ${trip.id} feasibility: ${feasibilityCheck.feasible ? 'FEASIBLE' : 'NOT FEASIBLE'}`);
      if (feasibilityCheck.feasible) {
        console.log(`  - Requires update: ${feasibilityCheck.requiresUpdate}`);
        console.log(`  - Suggested trip number: ${feasibilityCheck.suggestedTripNumber}`);
        if (feasibilityCheck.hadConflict) {
          console.log(`  - Conflict detected and resolved using: ${feasibilityCheck.conflictResolutionStrategy}`);
        }
      } else {
        console.log(`  - Reason: ${feasibilityCheck.reason}`);
      }
    }

    // Step 5: Test actual updates
    console.log('\nSTEP 5: Testing actual trip updates...');
    const updateResults = [];
    
    for (let i = 0; i < testTrips.length; i++) {
      const trip = testTrips[i];
      
      try {
        await client.query('BEGIN');
        
        console.log(`Updating trip ${trip.id}...`);
        
        const feasibilityCheck = await exceptionFlowManager.verifyAssignmentUpdateFeasibility(
          client, trip.id, targetAssignment
        );
        
        if (feasibilityCheck.feasible) {
          await exceptionFlowManager.updateTripToUseExistingAssignment(
            client, trip.id, targetAssignment, 1,
            { suggestedTripNumber: feasibilityCheck.suggestedTripNumber }
          );
          
          // Verify the update
          const verifyResult = await client.query(`
            SELECT assignment_id, trip_number FROM trip_logs WHERE id = $1
          `, [trip.id]);
          
          const updatedTrip = verifyResult.rows[0];
          console.log(`✅ Trip ${trip.id} updated successfully: assignment_id=${updatedTrip.assignment_id}, trip_number=${updatedTrip.trip_number}`);
          
          updateResults.push({
            tripId: trip.id,
            success: true,
            newTripNumber: updatedTrip.trip_number
          });
        } else {
          console.log(`⚠️ Trip ${trip.id} update skipped: ${feasibilityCheck.reason}`);
          updateResults.push({
            tripId: trip.id,
            success: false,
            reason: feasibilityCheck.reason
          });
        }
        
        await client.query('COMMIT');
        
      } catch (error) {
        await client.query('ROLLBACK');
        console.error(`❌ Trip ${trip.id} update failed: ${error.message}`);
        updateResults.push({
          tripId: trip.id,
          success: false,
          error: error.message
        });
      }
    }

    // Step 6: Verify results
    console.log('\nSTEP 6: Verifying constraint compliance...');
    
    const constraintCheckResult = await client.query(`
      SELECT 
        assignment_id,
        trip_number,
        COUNT(*) as duplicate_count
      FROM trip_logs
      WHERE assignment_id = $1
      GROUP BY assignment_id, trip_number
      HAVING COUNT(*) > 1
    `, [targetAssignment.id]);
    
    if (constraintCheckResult.rows.length > 0) {
      console.error('❌ CONSTRAINT VIOLATION DETECTED!');
      console.error('Duplicate (assignment_id, trip_number) combinations found:');
      constraintCheckResult.rows.forEach(row => {
        console.error(`  - Assignment ${row.assignment_id}, Trip Number ${row.trip_number}: ${row.duplicate_count} duplicates`);
      });
    } else {
      console.log('✅ NO CONSTRAINT VIOLATIONS - All trip numbers are unique per assignment');
    }
    
    // Show final trip assignment distribution
    const distributionResult = await client.query(`
      SELECT assignment_id, trip_number, id as trip_id
      FROM trip_logs
      WHERE assignment_id = $1
      ORDER BY trip_number
    `, [targetAssignment.id]);
    
    console.log('\nFinal trip distribution on target assignment:');
    distributionResult.rows.forEach(row => {
      console.log(`  - Trip ${row.trip_id}: trip_number=${row.trip_number}`);
    });
    
    const successCount = updateResults.filter(r => r.success).length;
    console.log(`\n📊 SUMMARY: ${successCount} of ${testTrips.length} trips updated successfully`);

    // Step 7: Cleanup
    console.log('\nSTEP 7: Cleaning up test data...');
    
    try {
      // Delete trip logs first (foreign key constraints)
      await client.query(`DELETE FROM trip_logs WHERE assignment_id IN (
        SELECT id FROM assignments WHERE assignment_code LIKE 'TEST-%'
      )`);
      
      // Delete assignments
      await client.query(`DELETE FROM assignments WHERE assignment_code LIKE 'TEST-%'`);
      
      // Delete test entities
      await client.query(`DELETE FROM drivers WHERE license_number = 'TEST123456'`);
      await client.query(`DELETE FROM locations WHERE name LIKE 'Test %'`);
      await client.query(`DELETE FROM dump_trucks WHERE truck_number = 'TEST-TRUCK-001'`);
      
      console.log('✅ Cleanup completed successfully');
      
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack:', error.stack);
  } finally {
    client.release();
    console.log('\n====================================');
    console.log('SIMPLE CONSTRAINT TEST COMPLETED');
    console.log('====================================');
  }
}

// Add a helper function to run tests with better error handling
async function runTestWithRetry() {
  try {
    await runSimpleConstraintTest();
  } catch (error) {
    console.error('Fatal test error:', error);
    process.exit(1);
  }
}

// Export for potential use by other test scripts
module.exports = { runSimpleConstraintTest };

// Run the test if this file is executed directly
if (require.main === module) {
  runTestWithRetry();
}
