#!/usr/bin/env node

/**
 * Comprehensive Test Suite for Dynamic Route Discovery System
 * 
 * This script runs all tests to validate the complete dynamic route discovery
 * implementation including backend logic, frontend updates, and real-time notifications.
 */

const { spawn } = require('child_process');
const path = require('path');

// Test configuration
const tests = [
  {
    name: 'Route Prediction Analysis',
    script: 'analyze_route_prediction.js',
    description: 'Analyzes current route prediction behavior and identifies issues',
    timeout: 30000
  },
  {
    name: 'Dynamic Route Discovery Core',
    script: 'test_dynamic_route_discovery.js',
    description: 'Tests core dynamic route discovery functionality',
    timeout: 60000
  },
  {
    name: 'Flexible Routing Verification',
    script: 'verify_flexible_routing.js',
    description: 'Verifies AutoAssignmentCreator flexible routing capabilities',
    timeout: 45000
  },
  {
    name: 'Trip Monitoring Dashboard Updates',
    script: 'test_trip_monitoring_updates.js',
    description: 'Tests dashboard updates with uncertainty indicators',
    timeout: 30000
  },
  {
    name: 'WebSocket Route Notifications',
    script: 'test_websocket_route_notifications.js',
    description: 'Tests real-time WebSocket notifications for route discovery',
    timeout: 45000
  }
];

// Test results tracking
let testResults = [];
let totalTests = tests.length;
let passedTests = 0;
let failedTests = 0;

// Run a single test
function runTest(test) {
  return new Promise((resolve) => {
    console.log(`\n🔧 Running: ${test.name}`);
    console.log(`📝 Description: ${test.description}`);
    console.log(`⏱️  Timeout: ${test.timeout / 1000}s`);
    console.log('=' .repeat(80));

    const startTime = Date.now();
    const testProcess = spawn('node', [path.join(__dirname, test.script)], {
      stdio: 'pipe',
      cwd: path.dirname(__dirname)
    });

    let stdout = '';
    let stderr = '';

    testProcess.stdout.on('data', (data) => {
      const output = data.toString();
      stdout += output;
      process.stdout.write(output);
    });

    testProcess.stderr.on('data', (data) => {
      const output = data.toString();
      stderr += output;
      process.stderr.write(output);
    });

    // Set timeout
    const timeoutId = setTimeout(() => {
      testProcess.kill('SIGTERM');
      const duration = Date.now() - startTime;
      
      const result = {
        name: test.name,
        status: 'TIMEOUT',
        duration,
        error: `Test timed out after ${test.timeout / 1000}s`
      };
      
      testResults.push(result);
      failedTests++;
      
      console.log(`\n⏰ TIMEOUT: ${test.name} (${duration}ms)`);
      resolve(result);
    }, test.timeout);

    testProcess.on('close', (code) => {
      clearTimeout(timeoutId);
      const duration = Date.now() - startTime;
      
      const result = {
        name: test.name,
        status: code === 0 ? 'PASS' : 'FAIL',
        duration,
        exitCode: code,
        stdout,
        stderr
      };

      if (code === 0) {
        passedTests++;
        console.log(`\n✅ PASSED: ${test.name} (${duration}ms)`);
      } else {
        failedTests++;
        console.log(`\n❌ FAILED: ${test.name} (${duration}ms) - Exit code: ${code}`);
        if (stderr) {
          console.log(`Error output: ${stderr.substring(0, 500)}...`);
        }
      }

      testResults.push(result);
      resolve(result);
    });

    testProcess.on('error', (error) => {
      clearTimeout(timeoutId);
      const duration = Date.now() - startTime;
      
      const result = {
        name: test.name,
        status: 'ERROR',
        duration,
        error: error.message
      };

      testResults.push(result);
      failedTests++;
      
      console.log(`\n💥 ERROR: ${test.name} (${duration}ms) - ${error.message}`);
      resolve(result);
    });
  });
}

// Generate test report
function generateReport() {
  console.log('\n' + '=' .repeat(80));
  console.log('🎯 COMPREHENSIVE TEST SUITE RESULTS');
  console.log('=' .repeat(80));

  console.log(`\n📊 Summary:`);
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests} ✅`);
  console.log(`   Failed: ${failedTests} ❌`);
  console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  console.log(`\n📋 Detailed Results:`);
  testResults.forEach((result, index) => {
    const statusIcon = result.status === 'PASS' ? '✅' : 
                      result.status === 'FAIL' ? '❌' : 
                      result.status === 'TIMEOUT' ? '⏰' : '💥';
    
    console.log(`   ${index + 1}. ${statusIcon} ${result.name}`);
    console.log(`      Status: ${result.status}`);
    console.log(`      Duration: ${result.duration}ms`);
    
    if (result.error) {
      console.log(`      Error: ${result.error}`);
    }
    
    if (result.exitCode !== undefined && result.exitCode !== 0) {
      console.log(`      Exit Code: ${result.exitCode}`);
    }
  });

  // Performance analysis
  console.log(`\n⚡ Performance Analysis:`);
  const totalDuration = testResults.reduce((sum, result) => sum + result.duration, 0);
  const avgDuration = totalDuration / testResults.length;
  const slowestTest = testResults.reduce((slowest, current) => 
    current.duration > slowest.duration ? current : slowest
  );
  const fastestTest = testResults.reduce((fastest, current) => 
    current.duration < fastest.duration ? current : fastest
  );

  console.log(`   Total Test Time: ${(totalDuration / 1000).toFixed(2)}s`);
  console.log(`   Average Test Time: ${(avgDuration / 1000).toFixed(2)}s`);
  console.log(`   Slowest Test: ${slowestTest.name} (${(slowestTest.duration / 1000).toFixed(2)}s)`);
  console.log(`   Fastest Test: ${fastestTest.name} (${(fastestTest.duration / 1000).toFixed(2)}s)`);

  // Feature validation
  console.log(`\n🎯 Feature Validation:`);
  const features = [
    { name: 'Route Prediction Analysis', test: 'Route Prediction Analysis' },
    { name: 'Dynamic Assignment Creation', test: 'Dynamic Route Discovery Core' },
    { name: 'Progressive Route Building', test: 'Dynamic Route Discovery Core' },
    { name: 'Flexible Routing Support', test: 'Flexible Routing Verification' },
    { name: 'Dashboard Uncertainty Indicators', test: 'Trip Monitoring Dashboard Updates' },
    { name: 'Real-Time WebSocket Notifications', test: 'WebSocket Route Notifications' }
  ];

  features.forEach(feature => {
    const testResult = testResults.find(r => r.name === feature.test);
    const status = testResult && testResult.status === 'PASS' ? '✅' : '❌';
    console.log(`   ${status} ${feature.name}`);
  });

  // Recommendations
  console.log(`\n💡 Recommendations:`);
  if (passedTests === totalTests) {
    console.log(`   🎉 All tests passed! Dynamic Route Discovery system is ready for deployment.`);
    console.log(`   ✅ System eliminates premature route predictions`);
    console.log(`   ✅ Progressive route building is working correctly`);
    console.log(`   ✅ Real-time updates and notifications are functional`);
    console.log(`   ✅ Performance targets are maintained`);
  } else {
    console.log(`   ⚠️  ${failedTests} test(s) failed. Review failed tests before deployment.`);
    
    const failedTestNames = testResults
      .filter(r => r.status !== 'PASS')
      .map(r => r.name);
    
    console.log(`   Failed tests: ${failedTestNames.join(', ')}`);
    console.log(`   🔧 Fix issues and re-run tests before proceeding`);
  }

  return passedTests === totalTests;
}

// Main test runner
async function runComprehensiveTests() {
  console.log('🚀 Starting Comprehensive Dynamic Route Discovery Test Suite');
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  console.log(`🔢 Total tests to run: ${totalTests}`);

  const overallStartTime = Date.now();

  // Run all tests sequentially
  for (const test of tests) {
    await runTest(test);
  }

  const overallDuration = Date.now() - overallStartTime;
  console.log(`\n⏱️  Total execution time: ${(overallDuration / 1000).toFixed(2)}s`);

  // Generate and display report
  const allTestsPassed = generateReport();

  // Exit with appropriate code
  process.exit(allTestsPassed ? 0 : 1);
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n\n🛑 Test suite interrupted by user');
  console.log('📊 Partial results:');
  generateReport();
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n\n🛑 Test suite terminated');
  console.log('📊 Partial results:');
  generateReport();
  process.exit(1);
});

// Run the comprehensive test suite
if (require.main === module) {
  runComprehensiveTests().catch((error) => {
    console.error('\n💥 Test suite execution failed:', error);
    process.exit(1);
  });
}

module.exports = { runComprehensiveTests, tests };
