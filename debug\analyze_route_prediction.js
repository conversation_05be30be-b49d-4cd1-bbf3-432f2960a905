#!/usr/bin/env node

/**
 * Analyze Route Prediction Logic
 * 
 * This script analyzes the current route prediction behavior in the Hauling QR Trip System
 * to identify where premature route predictions occur instead of dynamic route discovery.
 */

const { getClient } = require('../server/config/database');

async function analyzeRoutePrediction() {
  const client = await getClient();
  
  try {
    console.log('🔍 Analyzing Current Route Prediction Logic...\n');

    // 1. Analyze Assignment Creation Patterns
    console.log('📊 1. Assignment Creation Analysis:');
    console.log('=' .repeat(50));
    
    const assignmentAnalysis = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        a.status,
        ll.name as loading_location,
        ul.name as unloading_location,
        a.notes::text as creation_notes,
        a.created_at
      FROM assignments a
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.created_at >= CURRENT_DATE - INTERVAL '7 days'
      ORDER BY a.created_at DESC
      LIMIT 10
    `);

    console.log(`Found ${assignmentAnalysis.rows.length} recent assignments:`);
    assignmentAnalysis.rows.forEach(assignment => {
      const notes = assignment.creation_notes ? JSON.parse(assignment.creation_notes) : {};
      const isAutoCreated = notes.creation_method === 'auto_assignment';
      
      console.log(`   ${assignment.assignment_code}: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`      Status: ${assignment.status} | Auto-created: ${isAutoCreated ? 'Yes' : 'No'}`);
      
      if (isAutoCreated && notes.trigger_location) {
        console.log(`      🤖 Triggered at: ${notes.trigger_location.name} (${notes.trigger_location.type})`);
        console.log(`      📍 PREDICTION ISSUE: Route was predicted before truck visited all locations`);
      }
    });

    // 2. Analyze Trip Progression Patterns
    console.log('\n📊 2. Trip Progression Analysis:');
    console.log('=' .repeat(50));
    
    const tripAnalysis = await client.query(`
      SELECT 
        tl.id,
        tl.trip_number,
        tl.status,
        tl.actual_loading_location_id,
        tl.actual_unloading_location_id,
        ll.name as assigned_loading,
        ul.name as assigned_unloading,
        all_loc.name as actual_loading,
        aul_loc.name as actual_unloading,
        a.assignment_code,
        tl.created_at,
        tl.updated_at
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      LEFT JOIN locations all_loc ON tl.actual_loading_location_id = all_loc.id
      LEFT JOIN locations aul_loc ON tl.actual_unloading_location_id = aul_loc.id
      WHERE tl.created_at >= CURRENT_DATE - INTERVAL '1 day'
      ORDER BY tl.created_at DESC
      LIMIT 10
    `);

    console.log(`Found ${tripAnalysis.rows.length} recent trips:`);
    tripAnalysis.rows.forEach(trip => {
      console.log(`   Trip #${trip.trip_number} (${trip.assignment_code}): ${trip.status}`);
      console.log(`      Assigned Route: ${trip.assigned_loading} → ${trip.assigned_unloading}`);
      console.log(`      Actual Route: ${trip.actual_loading || 'Not started'} → ${trip.actual_unloading || 'Not visited'}`);
      
      // Check for premature predictions
      if (trip.assigned_unloading && !trip.actual_unloading && trip.status !== 'trip_completed') {
        console.log(`      🚨 PREDICTION ISSUE: Unloading location predicted but not yet visited`);
      }
    });

    // 3. Analyze AutoAssignmentCreator Behavior
    console.log('\n📊 3. AutoAssignmentCreator Analysis:');
    console.log('=' .repeat(50));
    
    const autoAssignmentAnalysis = await client.query(`
      SELECT 
        a.id,
        a.assignment_code,
        ll.name as loading_location,
        ul.name as unloading_location,
        a.notes::text as creation_notes,
        a.created_at
      FROM assignments a
      JOIN locations ll ON a.loading_location_id = ll.id
      JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE a.notes::text LIKE '%auto_assignment%'
      ORDER BY a.created_at DESC
      LIMIT 5
    `);

    console.log(`Found ${autoAssignmentAnalysis.rows.length} auto-created assignments:`);
    autoAssignmentAnalysis.rows.forEach(assignment => {
      const notes = JSON.parse(assignment.creation_notes);
      console.log(`   ${assignment.assignment_code}: ${assignment.loading_location} → ${assignment.unloading_location}`);
      console.log(`      Trigger Location: ${notes.trigger_location?.name} (${notes.trigger_location?.type})`);
      console.log(`      Based on: ${notes.based_on_assignment?.assignment_code}`);
      
      // Identify prediction logic
      if (notes.trigger_location?.type === 'loading' && assignment.unloading_location !== notes.trigger_location.name) {
        console.log(`      🔍 PREDICTION: Unloading location predicted from historical data`);
      } else if (notes.trigger_location?.type === 'unloading' && assignment.loading_location !== notes.trigger_location.name) {
        console.log(`      🔍 PREDICTION: Loading location predicted from historical data`);
      }
    });

    // 4. Analyze Current Route Display Logic
    console.log('\n📊 4. Route Display Analysis:');
    console.log('=' .repeat(50));
    
    // Check what information is being sent to frontend
    const activeTrips = await client.query(`
      SELECT 
        tl.id,
        tl.trip_number,
        tl.status,
        ll.name as loading_location,
        ul.name as unloading_location,
        tl.actual_loading_location_id,
        tl.actual_unloading_location_id,
        CASE 
          WHEN tl.actual_loading_location_id IS NOT NULL AND tl.actual_unloading_location_id IS NOT NULL THEN 'complete_route_known'
          WHEN tl.actual_loading_location_id IS NOT NULL AND tl.actual_unloading_location_id IS NULL THEN 'loading_known_unloading_predicted'
          WHEN tl.actual_loading_location_id IS NULL AND tl.actual_unloading_location_id IS NULL THEN 'route_predicted'
          ELSE 'unknown_state'
        END as route_knowledge_state
      FROM trip_logs tl
      JOIN assignments a ON tl.assignment_id = a.id
      LEFT JOIN locations ll ON a.loading_location_id = ll.id
      LEFT JOIN locations ul ON a.unloading_location_id = ul.id
      WHERE tl.status NOT IN ('trip_completed', 'cancelled')
      ORDER BY tl.created_at DESC
      LIMIT 5
    `);

    console.log(`Found ${activeTrips.rows.length} active trips:`);
    activeTrips.rows.forEach(trip => {
      console.log(`   Trip #${trip.trip_number}: ${trip.status}`);
      console.log(`      Route Display: ${trip.loading_location} → ${trip.unloading_location}`);
      console.log(`      Knowledge State: ${trip.route_knowledge_state}`);
      
      if (trip.route_knowledge_state === 'loading_known_unloading_predicted') {
        console.log(`      🎯 ISSUE: Dashboard shows predicted unloading location before truck visits it`);
      } else if (trip.route_knowledge_state === 'route_predicted') {
        console.log(`      🎯 ISSUE: Dashboard shows complete predicted route before any scans`);
      }
    });

    // 5. Identify Root Causes
    console.log('\n📊 5. Root Cause Analysis:');
    console.log('=' .repeat(50));
    
    console.log('🔍 Identified Issues:');
    console.log('   1. AutoAssignmentCreator creates complete routes (loading + unloading) immediately');
    console.log('   2. Trip Monitoring displays assignment routes as if they were confirmed');
    console.log('   3. No distinction between "predicted" and "confirmed" locations in UI');
    console.log('   4. WebSocket notifications send complete route information prematurely');
    
    console.log('\n🎯 Required Changes for Dynamic Route Discovery:');
    console.log('   1. Modify AutoAssignmentCreator to create partial assignments');
    console.log('   2. Update Trip Monitoring to show uncertainty indicators (?)');
    console.log('   3. Implement progressive route building based on actual scans');
    console.log('   4. Add route confirmation logic after physical QR scans');

    return true;

  } catch (error) {
    console.error('❌ Route prediction analysis failed:', error);
    return false;
  } finally {
    await client.release();
  }
}

// Run analysis if called directly
if (require.main === module) {
  analyzeRoutePrediction()
    .then((success) => {
      if (success) {
        console.log('\n✅ Route Prediction Analysis Completed');
        console.log('📝 Key Finding: System makes premature route predictions instead of dynamic discovery');
        console.log('🔄 Next: Implement dynamic route discovery system');
        process.exit(0);
      } else {
        console.log('\n❌ Route Prediction Analysis Failed');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n❌ Analysis execution failed:', error);
      process.exit(1);
    });
}

module.exports = { analyzeRoutePrediction };
