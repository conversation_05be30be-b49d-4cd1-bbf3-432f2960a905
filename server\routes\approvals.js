const express = require('express');
const router = express.Router();
const { query, getClient } = require('../config/database');
const auth = require('../middleware/auth');
const Joi = require('joi');
const { notifyExceptionCreated, notifyExceptionUpdated } = require('../websocket');
const {
  processApprovalAndUpdateTrip,
  createExceptionAndSetPending,
  EXCEPTION_STATES,
  TRIP_STATES
} = require('../utils/exception-flow-manager');
const { hybridExceptionManager } = require('../utils/hybrid-exception-manager');
const { AutoAssignmentCreator } = require('../utils/AutoAssignmentCreator');

// Validation schemas
const approvalSchema = Joi.object({
  trip_log_id: Joi.number().integer().required(),
  exception_type: Joi.string().valid(
    'route_deviation', 'Route Deviation', // Accept both formats
    'time_violation', 'Time Violation', 
    'equipment_issue', 'Equipment Issue', 
    'weather_delay', 'Weather Delay', 
    'manual_override', 'Manual Override',
    'other', 'Other'
  ).required(),
  exception_description: Joi.string().min(5).max(1000).required(),
  severity: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium'),
  reported_by: Joi.number().integer().optional()
});

const approvalDecisionSchema = Joi.object({
  decision: Joi.string().valid('approved', 'rejected').required(),
  notes: Joi.string().max(1000).optional().allow('')
});

// Enhanced logging
function logDebug(context, message, data = {}) {
  console.log(`[${new Date().toISOString()}] [APPROVALS] [${context}] ${message}`, JSON.stringify(data, null, 2));
}

function logError(context, error, additionalData = {}) {
  console.error(`[${new Date().toISOString()}] [APPROVALS] [${context}] ERROR:`, {
    message: error.message,
    stack: error.stack,
    ...additionalData
  });
}

// @route   GET /api/approvals
// @desc    Get all approval requests with enhanced filtering and performance
// @access  Private (Admin/Supervisor only)
router.get('/', auth, async (req, res) => {
  try {
    // Check user role
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: 'Access Denied',
        message: 'Only administrators and supervisors can view approvals'
      });
    }

    const {
      page = 1,
      limit = 10,
      status = '',
      exception_type = '',
      severity = '',
      truck_number = '',
      date_from = '',
      date_to = '',
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    const validSortColumns = ['created_at', 'exception_type', 'severity', 'status', 'requested_at'];
    const validSortOrders = ['asc', 'desc'];
    
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at';
    const sortDirection = validSortOrders.includes(sortOrder.toLowerCase()) ? sortOrder.toUpperCase() : 'DESC';

    // Build WHERE conditions
    let whereConditions = [];
    let queryParams = [];
    let paramCount = 0;

    if (status) {
      paramCount++;
      whereConditions.push(`a.status = $${paramCount}`);
      queryParams.push(status);
    }

    if (exception_type) {
      paramCount++;
      whereConditions.push(`a.exception_type = $${paramCount}`);
      queryParams.push(exception_type);
    }

    if (severity) {
      paramCount++;
      whereConditions.push(`a.severity = $${paramCount}`);
      queryParams.push(severity);
    }

    if (truck_number) {
      paramCount++;
      whereConditions.push(`dt.truck_number ILIKE $${paramCount}`);
      queryParams.push(`%${truck_number}%`);
    }

    if (date_from) {
      paramCount++;
      whereConditions.push(`a.created_at >= $${paramCount}`);
      queryParams.push(date_from);
    }

    if (date_to) {
      paramCount++;
      whereConditions.push(`a.created_at <= $${paramCount}`);
      queryParams.push(date_to);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Count total records with optimized query
    const countQuery = `
      SELECT COUNT(DISTINCT a.id) as total
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      JOIN assignments ass ON tl.assignment_id = ass.id
      JOIN dump_trucks dt ON ass.truck_id = dt.id
      ${whereClause}
    `;

    const countResult = await query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(totalItems / parseInt(limit));

    // Get approvals with optimized query
    paramCount++;
    const limitParam = paramCount;
    paramCount++;
    const offsetParam = paramCount;

    const approvalsQuery = `
      WITH approval_data AS (
        SELECT 
          a.*,
          tl.trip_number,
          tl.status as trip_status,
          tl.loading_start_time,
          tl.actual_loading_location_id,
          tl.actual_unloading_location_id,
          dt.truck_number,
          d.full_name as driver_name,
          ll.name as loading_location,
          ul.name as unloading_location,
          al.name as actual_loading_location,
          aul.name as actual_unloading_location,
          u1.email as reported_by_email,
          u2.email as reviewed_by_email,
          ass.id as assignment_id
        FROM approvals a
        JOIN trip_logs tl ON a.trip_log_id = tl.id
        JOIN assignments ass ON tl.assignment_id = ass.id
        JOIN dump_trucks dt ON ass.truck_id = dt.id
        LEFT JOIN drivers d ON ass.driver_id = d.id
        LEFT JOIN locations ll ON ass.loading_location_id = ll.id
        LEFT JOIN locations ul ON ass.unloading_location_id = ul.id
        LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
        LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
        LEFT JOIN users u1 ON a.reported_by = u1.id
        LEFT JOIN users u2 ON a.reviewed_by = u2.id
        ${whereClause}
      )
      SELECT * FROM approval_data
      ORDER BY ${sortColumn} ${sortDirection}
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;

    queryParams.push(parseInt(limit), offset);
    const approvalsResult = await query(approvalsQuery, queryParams);

    logDebug('GET_APPROVALS', 'Fetched approvals', {
      count: approvalsResult.rows.length,
      page,
      totalItems
    });

    res.json({
      success: true,
      data: approvalsResult.rows,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalItems,
        itemsPerPage: parseInt(limit),
        hasNextPage: parseInt(page) < totalPages,
        hasPrevPage: parseInt(page) > 1
      }
    });
  } catch (error) {
    logError('GET_APPROVALS', error, { query: req.query });
    
    res.status(500).json({
      success: false,
      error: 'Server Error',
      message: 'Failed to fetch approval requests'
    });
  }
});

// @route   POST /api/approvals
// @desc    Create new exception/approval request
// @access  Private
router.post('/', auth, async (req, res) => {
  const client = await getClient();
  
  try {
    // Validate input
    const { error } = approvalSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: error.details[0].message
      });
    }

    const {
      trip_log_id,
      exception_type,
      exception_description,
      severity,
      reported_by
    } = req.body;

    await client.query('BEGIN');

    // Verify trip log exists and get details
    const tripResult = await client.query(
      `SELECT tl.*, a.truck_id, dt.truck_number 
       FROM trip_logs tl
       JOIN assignments a ON tl.assignment_id = a.id
       JOIN dump_trucks dt ON a.truck_id = dt.id
       WHERE tl.id = $1`,
      [trip_log_id]
    );

    if (tripResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Trip log not found'
      });
    }

    const trip = tripResult.rows[0];

    // Check if exception already exists for this trip
    const existingException = await client.query(
      'SELECT id FROM approvals WHERE trip_log_id = $1 AND status = $2',
      [trip_log_id, 'pending']
    );

    if (existingException.rows.length > 0) {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        error: 'Conflict',
        message: 'Pending exception already exists for this trip'
      });
    }    // Create approval request
    const insertQuery = `
      INSERT INTO approvals (
        trip_log_id, exception_type, exception_description, 
        severity, reported_by, status, requested_at,
        created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, 'pending', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `;

    const result = await client.query(insertQuery, [
      trip_log_id,
      exception_type,
      exception_description,
      severity || 'medium',
      reported_by || req.user.id
    ]);    // Update trip log to mark as exception
    await client.query(
      `UPDATE trip_logs 
       SET is_exception = true, exception_reason = $1, status = 'exception_pending', updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [exception_description, trip_log_id]
    );

    await client.query('COMMIT');

    // Send real-time notification
    try {
      notifyExceptionCreated({
        id: result.rows[0].id,
        exception_type,
        description: exception_description,
        severity: severity || 'medium',
        trip_log_id,
        truck_number: trip.truck_number,
        created_at: result.rows[0].created_at
      });
    } catch (notifyError) {
      logError('NOTIFY_EXCEPTION', notifyError);
    }

    logDebug('CREATE_APPROVAL', 'Exception created', {
      approval_id: result.rows[0].id,
      trip_log_id
    });

    res.status(201).json({
      success: true,
      message: 'Exception reported successfully',
      data: result.rows[0]
    });

  } catch (error) {
    await client.query('ROLLBACK');
    logError('CREATE_APPROVAL', error, { body: req.body });
    
    res.status(500).json({
      success: false,
      error: 'Server Error',
      message: 'Failed to create approval request'
    });
  } finally {
    client.release();
  }
});

// @route   PUT /api/approvals/:id
// @desc    Update approval decision with enhanced workflow
// @access  Private (Admin/Supervisor only)
router.put('/:id', auth, async (req, res) => {
  const client = await getClient();
  const { id } = req.params; // Extract id outside try block to fix scope issue
  
  try {
    // Check user role
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      logError('PUT_APPROVAL_ROLE_CHECK', 'Access denied', { user_role: req.user.role, approval_id: id });
      return res.status(403).json({
        success: false,
        error: 'Access Denied',
        message: 'Only administrators and supervisors can approve/reject exceptions'
      });
    }

    logDebug('PUT_APPROVAL_START', 'Processing approval request', { approval_id: id, body: req.body });

    // Validate input
    const { error } = approvalDecisionSchema.validate(req.body);
    if (error) {
      logError('PUT_APPROVAL_VALIDATION', 'Validation failed', { error: error.details[0].message, body: req.body });
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: error.details[0].message
      });
    }    const { decision, notes } = req.body;

    // Use hybrid exception manager for enhanced processing
    try {
      const result = await hybridExceptionManager.processHybridApproval(id, decision, req.user.id, notes);

      logDebug('PUT_APPROVAL_SUCCESS', 'Hybrid approval processed successfully', {
        approval_id: id,
        decision,
        is_adaptive: result.isAdaptive,
        adaptation_strategy: result.adaptationStrategy,
        new_trip_status: result.trip_update?.new_status
      });

      // Get complete approval data for response
      const completeDataResult = await client.query(`
        SELECT 
          a.*,
          tl.trip_number,
          tl.status as trip_status,
          tl.notes as trip_notes,
          dt.truck_number,
          d.full_name as driver_name,
          u.email as reviewed_by_email,
          ll.name as loading_location_name,
          ul.name as unloading_location_name,
          ass.status as assignment_status
        FROM approvals a
        JOIN trip_logs tl ON a.trip_log_id = tl.id
        JOIN assignments ass ON tl.assignment_id = ass.id
        JOIN dump_trucks dt ON ass.truck_id = dt.id
        LEFT JOIN drivers d ON ass.driver_id = d.id
        LEFT JOIN users u ON a.reviewed_by = u.id
        LEFT JOIN locations ll ON ass.loading_location_id = ll.id
        LEFT JOIN locations ul ON ass.unloading_location_id = ul.id
        WHERE a.id = $1
      `, [id]);

      // Send real-time notification
      try {
        notifyExceptionUpdated(completeDataResult.rows[0], decision);
      } catch (notifyError) {
        logError('NOTIFY_DECISION', notifyError);
      }

      // Format response message
      let responseMessage = `Exception ${decision} successfully`;
      if (decision === 'approved') {
        responseMessage = result.trip_update?.assignment_updated 
          ? `Route deviation approved - trip can now proceed with revised route`
          : `Exception approved - trip can now proceed`;
      } else {
        responseMessage = `Exception rejected - trip has been cancelled`;
      }

      res.json({
        success: true,
        message: responseMessage,
        data: completeDataResult.rows[0],
        trip_update: result.trip_update
      });

    } catch (error) {
      throw error; // Re-throw to be caught by outer catch block
    }
  } catch (error) {
    // Attempt rollback only if there's an active transaction
    try {
      await client.query('ROLLBACK');
    } catch (rollbackError) {
      // Log rollback failure but don't let it override the original error
      logError('ROLLBACK_FAILED', rollbackError, {
        approval_id: req.params.id, // Use req.params.id instead of undefined id
        original_error: error.message
      });
    }
    
    logError('UPDATE_APPROVAL', error, { 
      id, 
      body: req.body,
      user_id: req.user?.id,
      error_code: error.code,
      error_detail: error.detail,
      error_constraint: error.constraint
    });
    
    // Handle duplicate assignment constraint violations
    if (error.code === '23505' && error.constraint === 'idx_assignments_exact_duplicate') {
      return res.status(409).json({
        success: false,
        error: 'Duplicate Assignment',
        message: 'Cannot create duplicate assignment with the same truck, loading and unloading locations'
      });
    }
    
    // Handle trip_logs unique constraint violation
    if (error.code === '23505' && error.constraint === 'trip_logs_assignment_id_trip_number_key') {
      return res.status(409).json({
        success: false,
        error: 'Trip Assignment Conflict',
        message: 'Unable to reassign trip due to duplicate trip number. The system attempted to resolve this automatically with multiple retry attempts but failed. This may indicate heavy concurrent activity. Please try again in a moment or contact support if the issue persists.'
      });
    }
    
    // Handle transaction aborted errors (cascade from constraint violations)
    if (error.message && error.message.includes('current transaction is aborted')) {
      return res.status(500).json({
        success: false,
        error: 'Transaction Error',
        message: 'The operation was interrupted due to a database constraint violation. Please try again.'
      });
    }
    
    // Handle any other unique constraint violations
    if (error.code === '23505') {
      return res.status(409).json({
        success: false,
        error: 'Duplicate Record',
        message: 'A record with these values already exists'
      });
    }
    
    // Handle custom duplicate assignments detected by our check
    if (error.message && error.message.includes('Duplicate assignment detected')) {
      return res.status(409).json({
        success: false,
        error: 'Duplicate Assignment',
        message: 'An active assignment already exists for this truck and route'
      });
    }
    
    // Handle assignment update feasibility errors
    if (error.message && error.message.includes('Cannot update trip to use existing assignment')) {
      return res.status(409).json({
        success: false,
        error: 'Assignment Update Failed',
        message: 'Cannot update trip assignment due to conflicts with existing assignments. Please try again or contact support.'
      });
    }
    
    // Handle max retry errors from trip assignment updates
    if (error.message && error.message.includes('Failed to assign trip to existing assignment after') && error.message.includes('attempts')) {
      return res.status(503).json({
        success: false,
        error: 'Service Temporarily Unavailable',
        message: 'The system is experiencing high load and cannot complete the trip assignment update. Please try again in a few moments.'
      });
    }
    
    // Provide more specific error messages
    if (error.code === '22P02') {
      return res.status(400).json({
        success: false,
        error: 'Data Validation Error',
        message: 'Invalid data format in request'
      });
    }

    if (error.code === '23503') {
      return res.status(400).json({
        success: false,
        error: 'Reference Error',
        message: 'Referenced record not found (foreign key constraint)'
      });
    }

    if (error.message && error.message.includes('invalid input value for enum')) {
      return res.status(400).json({
        success: false,
        error: 'Invalid Enum Value',
        message: 'Invalid status or type value provided'
      });
    }

    if (error.message.includes('foreign key')) {
      return res.status(400).json({
        success: false,
        error: 'Reference Error',
        message: 'Referenced record not found'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Server Error',
      message: 'Failed to update approval request',
      debug: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  } finally {
    client.release();
  }
});

// Enhanced exception approval handling with better duplicate prevention
async function handleApprovedException(client, approval) {
  const { trip_log_id, reviewed_by, exception_type, exception_description } = approval;

  // Check if this is a route deviation for loading
  if (exception_description && exception_description.includes('Loading at')) {
    // Parse trip notes to get pending assignment ID and flow information
    let tripNotes;
    try {
      tripNotes = approval.trip_notes ? JSON.parse(approval.trip_notes) : {};
    } catch (e) {
      logError('PARSE_TRIP_NOTES', 'Failed to parse trip notes', { 
        trip_notes: approval.trip_notes 
      });
      tripNotes = {};
    }    
    
    if (tripNotes.pending_assignment_id) {
      try {
        // Lock the trip_log row to prevent concurrent updates
        await client.query('SELECT id FROM trip_logs WHERE id = $1 FOR UPDATE', [trip_log_id]);
        
        // Enhanced idempotency check to prevent duplicate processing
        const tripStatusCheck = await client.query(
          `SELECT status, exception_approved_at, assignment_id FROM trip_logs WHERE id = $1`,
          [trip_log_id]
        );
        
        if (tripStatusCheck.rows.length === 0) {
          logError('APPROVE_ROUTE_DEVIATION', 'Trip not found', { trip_log_id });
          return; // Trip not found - exit early
        }
        
        // Trip is already processed if either:
        // 1. Status is not 'exception_pending' (it was moved to another state)
        // 2. exception_approved_at is not null (it was already approved)
        // 3. If the assignment_id matches the pending_assignment_id in trip notes (approval already processed)
        let isAlreadyProcessed = tripStatusCheck.rows[0].status !== 'exception_pending' || 
                                tripStatusCheck.rows[0].exception_approved_at !== null;
                                
        // Additional check - if the trip's current assignment matches the pending assignment
        // in the trip notes, then the approval has already been processed
        if (!isAlreadyProcessed && tripNotes.pending_assignment_id && 
            tripStatusCheck.rows[0].assignment_id === parseInt(tripNotes.pending_assignment_id)) {
          isAlreadyProcessed = true;
        }
        
        if (isAlreadyProcessed) {
          logDebug('APPROVE_ROUTE_DEVIATION', 'Trip already processed - preventing duplicate processing', {
            trip_log_id,
            status: tripStatusCheck.rows[0].status,
            has_exception_approval_timestamp: tripStatusCheck.rows[0].exception_approved_at !== null,
            current_assignment_id: tripStatusCheck.rows[0].assignment_id,
            pending_assignment_id: tripNotes.pending_assignment_id
          });
          return; // Already processed - exit early to prevent duplicates
        }
        
        // Verify the pending assignment exists before updating
        const assignmentCheck = await client.query(
          `SELECT a.id, a.status, a.truck_id, a.driver_id, 
                 a.loading_location_id, a.unloading_location_id,
                 dt.truck_number,
                 l1.name as loading_location,
                 l2.name as unloading_location
           FROM assignments a
           JOIN dump_trucks dt ON a.truck_id = dt.id
           JOIN locations l1 ON a.loading_location_id = l1.id
           JOIN locations l2 ON a.unloading_location_id = l2.id
           WHERE a.id = $1`,
          [tripNotes.pending_assignment_id]
        );

        if (assignmentCheck.rows.length === 0) {
          logError('APPROVE_ROUTE_DEVIATION', 'Assignment not found', {
            pending_assignment_id: tripNotes.pending_assignment_id,
            trip_log_id
          });
          // Fall back to just updating trip status
          await client.query(`
            UPDATE trip_logs 
            SET status = 'loading_start',
                exception_approved_by = $1,
                exception_approved_at = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2
            AND status = 'exception_pending'
            AND exception_approved_at IS NULL
          `, [reviewed_by, trip_log_id]);
          return;
        }

        // Check for duplicate active assignments (same truck, loading location, unloading location)
        const assignment = assignmentCheck.rows[0];
        
        // Get current assignment from trip log
        const currentAssignmentResult = await client.query(
          `SELECT a.id, a.truck_id, a.loading_location_id, a.unloading_location_id, a.status
           FROM assignments a
           JOIN trip_logs tl ON tl.assignment_id = a.id
           WHERE tl.id = $1`,
          [trip_log_id]
        );
        
        // Check if this is a multiple deviation case
        const isMultipleDeviation = tripNotes.deviation_count === 'multiple';
        const revisedFlow = tripNotes.revised_flow_pattern || 
              `${assignment.loading_location} → ${assignment.unloading_location} → ${assignment.loading_location}`;
        
        // For multiple deviations, we want to prioritize the newest assignment
        if (isMultipleDeviation) {
          logDebug('APPROVE_ROUTE_DEVIATION', 'Processing multiple deviation case', {
            trip_log_id,
            deviation_count: tripNotes.deviation_count,
            previous_deviation_ids: tripNotes.previous_deviation_ids || []
          });
        }
        
        // Only check for duplicates if the new assignment is different
        if (currentAssignmentResult.rows.length > 0 && 
            currentAssignmentResult.rows[0].id !== assignment.id) {
          
          // Check for duplicate active assignments (excluding current and pending assignments)
          const duplicateCheck = await client.query(
            `SELECT id, status, created_at 
             FROM assignments 
             WHERE id != $1 AND id != $2
             AND truck_id = $3
             AND loading_location_id = $4 
             AND unloading_location_id = $5
             AND status IN ('assigned', 'in_progress')
             ORDER BY created_at DESC
             LIMIT 1`,
            [
              assignment.id, 
              currentAssignmentResult.rows[0].id,
              assignment.truck_id,
              assignment.loading_location_id,
              assignment.unloading_location_id
            ]
          );
          
          if (duplicateCheck.rows.length > 0) {
            const duplicateAssignment = duplicateCheck.rows[0];
            
            logDebug('APPROVE_ROUTE_DEVIATION', 'Found existing assignment that matches the deviation path', {
              pending_assignment_id: tripNotes.pending_assignment_id,
              duplicate_assignment_id: duplicateAssignment.id,
              duplicate_status: duplicateAssignment.status,
              duplicate_created: duplicateAssignment.created_at,
              truck_id: assignment.truck_id,
              loading_location_id: assignment.loading_location_id,
              unloading_location_id: assignment.unloading_location_id,
              is_multiple_deviation: isMultipleDeviation
            });
            
            // Check which assignment is newer (use the newest one)
            let assignmentIdToUse;
            if (new Date(duplicateAssignment.created_at) > new Date(assignment.created_at)) {
              assignmentIdToUse = duplicateAssignment.id;
              logDebug('ASSIGNMENT_CHOICE', 'Using newer existing assignment', {
                chosen_assignment_id: assignmentIdToUse,
                reason: 'newer_timestamp'
              });
            } else {
              assignmentIdToUse = tripNotes.pending_assignment_id;
              logDebug('ASSIGNMENT_CHOICE', 'Using pending assignment', {
                chosen_assignment_id: assignmentIdToUse,
                reason: 'pending_is_newer'
              });
            }
            
            // Update trip to use the chosen assignment
            await client.query(`
              UPDATE trip_logs 
              SET assignment_id = $1, 
                  status = 'loading_start',
                  exception_approved_by = $2,
                  exception_approved_at = CURRENT_TIMESTAMP,
                  notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb,
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $4
              AND status = 'exception_pending'
              AND exception_approved_at IS NULL
            `, [
              assignmentIdToUse, 
              reviewed_by, 
              JSON.stringify({ 
                using_existing_assignment: assignmentIdToUse === duplicateAssignment.id,
                original_pending_assignment_id: tripNotes.pending_assignment_id,
                revised_flow: revisedFlow,
                approval_timestamp: new Date().toISOString(),
                is_multiple_deviation: isMultipleDeviation,
                previous_deviation_ids: tripNotes.previous_deviation_ids || []
              }),
              trip_log_id
            ]);
            
            // Update the assignment status to in_progress if it's not already
            await client.query(`
              UPDATE assignments
              SET status = 'in_progress',
                  start_time = COALESCE(start_time, CURRENT_TIMESTAMP),
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $1
              AND status = 'assigned'
            `, [assignmentIdToUse]);
            
            // Complete this function early since we've handled the approval using an existing assignment
            return;
          }
        }
        
        // Log the revised flow being approved
        logDebug('APPROVE_ROUTE_DEVIATION', 'Approving revised flow pattern', {
          original_assignment_id: tripNotes.original_assignment_id,
          new_assignment_id: assignment.id,
          revised_flow: revisedFlow,
          is_multiple_deviation: isMultipleDeviation
        });
        
        // All checks passed, update trip log to use new assignment
        // This update records the approved exception and makes the new assignment the standard flow
        await client.query(`
          UPDATE trip_logs 
          SET assignment_id = $1, 
              status = 'loading_start',
              exception_approved_by = $2,
              exception_approved_at = CURRENT_TIMESTAMP,
              notes = COALESCE(notes::jsonb, '{}'::jsonb) || $3::jsonb,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $4
          AND status = 'exception_pending'
          AND exception_approved_at IS NULL
        `, [
          tripNotes.pending_assignment_id, 
          reviewed_by, 
          JSON.stringify({ 
            revised_flow: revisedFlow, 
            approval_timestamp: new Date().toISOString(),
            original_assignment_id: tripNotes.original_assignment_id,
            is_standard_flow: true,  // Mark this as the new standard flow
            flow_type: 'revised',    // Indicate this is a revised flow from an exception
            is_multiple_deviation: isMultipleDeviation,
            previous_deviation_ids: tripNotes.previous_deviation_ids || []
          }),
          trip_log_id
        ]);        // Update the new assignment to assigned first (from pending_approval status)
        // This ensures it's properly activated before being set to in_progress
        await client.query(`
          UPDATE assignments
          SET status = 'assigned',
              assigned_date = CURRENT_DATE, -- Ensure assigned_date is set
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          AND status = 'pending_approval'
        `, [tripNotes.pending_assignment_id]);
        
        // Then update to in_progress if trip is actually starting
        await client.query(`
          UPDATE assignments
          SET status = 'in_progress',
              start_time = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
          AND status = 'assigned'
        `, [tripNotes.pending_assignment_id]);
        
        // Update the original assignment to completed if it's referenced in trip notes
        // and it's a first deviation (for multiple deviations, keep original assignments active)
        if (tripNotes.original_assignment_id && !isMultipleDeviation) {
          await client.query(`
            UPDATE assignments
            SET status = 
                CASE
                  WHEN status = 'in_progress' THEN 'completed'
                  ELSE status
                END,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = $1
            AND status = 'in_progress'
          `, [tripNotes.original_assignment_id]);
        }        // Get location details for logging
        const locationDetails = await client.query(`
          SELECT 
            a.id, 
            a.truck_id, 
            a.driver_id,
            a.loading_location_id, 
            a.unloading_location_id,
            dt.truck_number,
            l1.name as loading_location_name,
            l2.name as unloading_location_name
          FROM assignments a
          JOIN dump_trucks dt ON a.truck_id = dt.id
          JOIN locations l1 ON a.loading_location_id = l1.id
          JOIN locations l2 ON a.unloading_location_id = l2.id
          WHERE a.id = $1
        `, [tripNotes.pending_assignment_id]);
        
        const locationInfo = locationDetails.rows.length > 0 ? locationDetails.rows[0] : null;
        
        logDebug('APPROVE_ROUTE_DEVIATION', 'Route deviation approved with revised flow', {
          trip_log_id,
          new_assignment_id: tripNotes.pending_assignment_id,
          revised_flow: revisedFlow,
          loading_location: locationInfo ? locationInfo.loading_location_name : 'unknown',
          unloading_location: locationInfo ? locationInfo.unloading_location_name : 'unknown',
          is_multiple_deviation: isMultipleDeviation
        });
      } catch (error) {
        logError('APPROVE_ROUTE_DEVIATION', error, {
          pending_assignment_id: tripNotes.pending_assignment_id,
          trip_log_id,
          is_multiple_deviation: tripNotes.deviation_count === 'multiple'
        });
        
        // If it's not our custom error, re-throw to be caught in the main handler
        if (!error.message.includes('Duplicate assignment')) {
          throw error;
        }
        
        // Fall back to just updating trip status without changing the assignment
        await client.query(`
          UPDATE trip_logs 
          SET status = 'loading_start',
              exception_approved_by = $1,
              exception_approved_at = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $2
          AND status = 'exception_pending'
          AND exception_approved_at IS NULL
        `, [reviewed_by, trip_log_id]);
        return;
      }
    } else {
      // Just update trip status to allow continuation
      await client.query(`
        UPDATE trip_logs 
        SET status = 'loading_start',
            exception_approved_by = $1,
            exception_approved_at = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
        AND status = 'exception_pending'
        AND exception_approved_at IS NULL
      `, [reviewed_by, trip_log_id]);
    }
  } else {
    // Handle other exceptions (including unloading location deviations) with auto-assignment creation
    await handleOtherExceptionWithAutoAssignment(client, approval, reviewed_by);
  }
}

// Handle other exceptions (including unloading location deviations) with auto-assignment creation
async function handleOtherExceptionWithAutoAssignment(client, approval, reviewed_by) {
  const { trip_log_id, exception_description } = approval;

  try {
    // Check if this is an unloading location deviation that needs auto-assignment
    if (exception_description && exception_description.includes('Unloading at')) {
      logDebug('AUTO_ASSIGNMENT_APPROVAL', 'Processing unloading location exception for auto-assignment', {
        trip_log_id,
        exception_description
      });

      // Get trip and truck details
      const tripDetailsResult = await client.query(`
        SELECT
          tl.id, tl.assignment_id, tl.actual_unloading_location_id,
          a.truck_id, a.loading_location_id, a.unloading_location_id,
          dt.truck_number,
          ll.name as original_loading_location,
          ul.name as original_unloading_location,
          aul.name as actual_unloading_location
        FROM trip_logs tl
        JOIN assignments a ON tl.assignment_id = a.id
        JOIN dump_trucks dt ON a.truck_id = dt.id
        LEFT JOIN locations ll ON a.loading_location_id = ll.id
        LEFT JOIN locations ul ON a.unloading_location_id = ul.id
        LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
        WHERE tl.id = $1
      `, [trip_log_id]);

      if (tripDetailsResult.rows.length > 0) {
        const tripDetails = tripDetailsResult.rows[0];

        // Only create auto-assignment if we have actual unloading location
        if (tripDetails.actual_unloading_location_id) {
          logDebug('AUTO_ASSIGNMENT_APPROVAL', 'Creating auto-assignment for unloading location deviation', {
            truck_number: tripDetails.truck_number,
            original_route: `${tripDetails.original_loading_location} → ${tripDetails.original_unloading_location}`,
            new_route: `${tripDetails.original_loading_location} → ${tripDetails.actual_unloading_location}`
          });

          // Get truck and location objects for AutoAssignmentCreator
          const truckResult = await client.query(`
            SELECT id, truck_number, status FROM dump_trucks WHERE id = $1
          `, [tripDetails.truck_id]);

          const locationResult = await client.query(`
            SELECT id, name, type, status FROM locations WHERE id = $1
          `, [tripDetails.actual_unloading_location_id]);

          if (truckResult.rows.length > 0 && locationResult.rows.length > 0) {
            const truck = truckResult.rows[0];
            const location = locationResult.rows[0];

            // Create auto-assignment using AutoAssignmentCreator with preserved original loading location
            const autoAssignmentCreator = new AutoAssignmentCreator();

            const autoAssignment = await autoAssignmentCreator.createAutoAssignment({
              truck,
              location,
              client,
              userId: reviewed_by,
              preserveOriginalRoute: {
                loadingLocationId: tripDetails.loading_location_id,
                loadingLocationName: tripDetails.original_loading_location
              }
            });

            logDebug('AUTO_ASSIGNMENT_APPROVAL', 'Auto-assignment created successfully from exception approval', {
              trip_log_id,
              auto_assignment_id: autoAssignment.id,
              auto_assignment_code: autoAssignment.assignment_code,
              route: `${autoAssignment.loading_location_name} → ${autoAssignment.unloading_location_name}`
            });

            // Update trip log to mark as approved with auto-assignment reference
            await client.query(`
              UPDATE trip_logs
              SET exception_approved_by = $1,
                  exception_approved_at = CURRENT_TIMESTAMP,
                  notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = $3
              AND exception_approved_at IS NULL
            `, [
              reviewed_by,
              JSON.stringify({
                auto_assignment_created: true,
                auto_assignment_id: autoAssignment.id,
                auto_assignment_code: autoAssignment.assignment_code,
                approval_method: 'auto_assignment_creation'
              }),
              trip_log_id
            ]);

            return;
          }
        }
      }
    }

    // Fallback: For other exceptions or if auto-assignment creation fails, just mark as approved
    await client.query(`
      UPDATE trip_logs
      SET exception_approved_by = $1,
          exception_approved_at = CURRENT_TIMESTAMP,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $2
      AND exception_approved_at IS NULL
    `, [reviewed_by, trip_log_id]);

  } catch (error) {
    logError('AUTO_ASSIGNMENT_APPROVAL', error, {
      trip_log_id,
      exception_description
    });

    // Fallback: Just mark as approved if auto-assignment creation fails
    await client.query(`
      UPDATE trip_logs
      SET exception_approved_by = $1,
          exception_approved_at = CURRENT_TIMESTAMP,
          notes = COALESCE(notes::jsonb, '{}'::jsonb) || $2::jsonb,
          updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
      AND exception_approved_at IS NULL
    `, [
      reviewed_by,
      JSON.stringify({
        auto_assignment_creation_failed: true,
        error: error.message
      }),
      trip_log_id
    ]);
  }
}

// Handle rejected exception logic
async function handleRejectedException(client, approval) {
  const { trip_log_id } = approval;
  
  // Mark trip as cancelled due to rejected exception
  await client.query(`
    UPDATE trip_logs 
    SET status = 'cancelled',
        exception_approved_by = $1,
        exception_approved_at = CURRENT_TIMESTAMP,
        notes = COALESCE(notes, '{}')::jsonb || '{"cancellation_reason": "Exception rejected"}'::jsonb,
        updated_at = CURRENT_TIMESTAMP
    WHERE id = $2
  `, [approval.reviewed_by, trip_log_id]);

  logDebug('REJECT_EXCEPTION', 'Exception rejected, trip cancelled', {
    trip_log_id
  });
}

// @route   GET /api/approvals/stats
// @desc    Get exception statistics with performance optimization
// @access  Private
router.get('/stats', auth, async (req, res) => {
  try {
    const { days = 30 } = req.query;
    
    // Validate days parameter
    const daysInt = parseInt(days);
    if (isNaN(daysInt) || daysInt < 1 || daysInt > 365) {
      return res.status(400).json({
        success: false,
        error: 'Invalid Parameter',
        message: 'Days parameter must be a number between 1 and 365'
      });
    }

    // Use CTE for better performance
    const statsQuery = `
      WITH date_range AS (
        SELECT CURRENT_DATE - INTERVAL '${daysInt} days' as start_date
      ),
      approval_stats AS (
        SELECT 
          COUNT(*) as total_exceptions,
          COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
          COUNT(CASE WHEN severity = 'critical' THEN 1 END) as critical,
          COUNT(CASE WHEN severity = 'high' THEN 1 END) as high,
          COUNT(CASE WHEN severity = 'medium' THEN 1 END) as medium,
          COUNT(CASE WHEN severity = 'low' THEN 1 END) as low,
          AVG(CASE 
            WHEN status != 'pending' AND reviewed_at IS NOT NULL 
            THEN EXTRACT(EPOCH FROM (reviewed_at - requested_at))/3600 
          END) as avg_resolution_hours
        FROM approvals, date_range
        WHERE created_at >= date_range.start_date
      ),
      trip_stats AS (
        SELECT COUNT(*) as total_trips
        FROM trip_logs, date_range
        WHERE created_at >= date_range.start_date
      ),
      exception_types AS (
        SELECT 
          exception_type,
          COUNT(*) as count,
          ROUND(COUNT(*) * 100.0 / NULLIF(SUM(COUNT(*)) OVER(), 0), 1) as percentage
        FROM approvals, date_range
        WHERE created_at >= date_range.start_date
        GROUP BY exception_type
      ),
      daily_trends AS (
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as exceptions,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM approvals, date_range
        WHERE created_at >= date_range.start_date
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 7
      )
      SELECT 
        json_build_object(
          'summary', (SELECT row_to_json(approval_stats) FROM approval_stats),
          'total_trips', (SELECT total_trips FROM trip_stats),
          'exception_rate', ROUND(
            (SELECT total_exceptions FROM approval_stats)::numeric / 
            NULLIF((SELECT total_trips FROM trip_stats), 0) * 100, 
            2
          ),
          'types', (SELECT json_agg(et) FROM exception_types et),
          'daily_trends', (SELECT json_agg(dt) FROM daily_trends dt)
        ) as stats
    `;

    const result = await query(statsQuery);
    const stats = result.rows[0].stats;

    logDebug('GET_STATS', 'Statistics generated', {
      days: daysInt,
      total_exceptions: stats.summary.total_exceptions
    });

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    logError('GET_STATS', error, { query: req.query });
    
    res.status(500).json({
      success: false,
      error: 'Server Error',
      message: 'Failed to fetch exception statistics'
    });
  }
});

// @route   GET /api/approvals/:id
// @desc    Get specific approval request details
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const { id } = req.params;

    const approvalQuery = `
      SELECT 
        a.*,
        tl.trip_number,
        tl.status as trip_status,
        tl.loading_start_time,
        tl.loading_end_time,
        tl.unloading_start_time,
        tl.unloading_end_time,
        tl.actual_loading_location_id,
        tl.actual_unloading_location_id,
        dt.truck_number,
        dt.license_plate,
        d.full_name as driver_name,
        d.employee_id as driver_employee_id,
        ll.name as loading_location,
        ul.name as unloading_location,
        al.name as actual_loading_location,
        aul.name as actual_unloading_location,
        u1.email as reported_by_email,
        u1.full_name as reported_by_name,
        u2.email as reviewed_by_email,
        u2.full_name as reviewed_by_name,
        json_build_object(
          'id', ass.id,
          'assignment_code', ass.assignment_code,
          'status', ass.status,
          'priority', ass.priority,
          'expected_loads_per_day', ass.expected_loads_per_day
        ) as assignment_details
      FROM approvals a
      JOIN trip_logs tl ON a.trip_log_id = tl.id
      JOIN assignments ass ON tl.assignment_id = ass.id
      JOIN dump_trucks dt ON ass.truck_id = dt.id
      LEFT JOIN drivers d ON ass.driver_id = d.id
      LEFT JOIN locations ll ON ass.loading_location_id = ll.id
      LEFT JOIN locations ul ON ass.unloading_location_id = ul.id
      LEFT JOIN locations al ON tl.actual_loading_location_id = al.id
      LEFT JOIN locations aul ON tl.actual_unloading_location_id = aul.id
      LEFT JOIN users u1 ON a.reported_by = u1.id
      LEFT JOIN users u2 ON a.reviewed_by = u2.id
      WHERE a.id = $1
    `;

    const result = await query(approvalQuery, [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Approval request not found'
      });
    }

    res.json({
      success: true,
      data: result.rows[0]
    });

  } catch (error) {
    logError('GET_APPROVAL_DETAIL', error, { id });
    
    res.status(500).json({
      success: false,
      error: 'Server Error',
      message: 'Failed to fetch approval request details'
    });
  }
});

// @route   GET /api/approvals/pending/count
// @desc    Get count of pending approvals for notification badge
// @access  Private (Admin/Supervisor only)
router.get('/pending/count', auth, async (req, res) => {
  try {
    if (!['admin', 'supervisor'].includes(req.user.role)) {
      return res.json({
        success: true,
        data: { count: 0 }
      });
    }

    const result = await query(`
      SELECT COUNT(*) as count
      FROM approvals
      WHERE status = 'pending'
    `);

    res.json({
      success: true,
      data: {
        count: parseInt(result.rows[0].count)
      }
    });

  } catch (error) {
    logError('GET_PENDING_COUNT', error);
    
    res.json({
      success: true,
      data: { count: 0 }
    });
  }
});

module.exports = router;