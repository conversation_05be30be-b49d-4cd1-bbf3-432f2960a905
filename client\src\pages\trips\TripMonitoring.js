import { useState, useEffect, useCallback, useMemo } from 'react';
import { tripsAPI, assignmentsAPI } from '../../services/api';
import TripsTable from './components/TripsTable';
import TripDetailModal from './components/TripDetailModal';
import toast from 'react-hot-toast';

const TripMonitoring = () => {
  const [trips, setTrips] = useState([]);
  const [activeAssignments, setActiveAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
    hasNextPage: false,
    hasPrevPage: false
  });

  // Modal states
  const [showTripDetailModal, setShowTripDetailModal] = useState(false);
  const [selectedTrip, setSelectedTrip] = useState(null);

  // Filter and search states
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    assignment_id: '',
    date_from: '',
    date_to: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  // Auto-refresh interval
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [refreshInterval, setRefreshInterval] = useState(30); // seconds

  // Load trips with error handling
  const loadTrips = useCallback(async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pagination.itemsPerPage,
        ...filters
      };

      const response = await tripsAPI.getAll({ params });
      setTrips(response.data.data);
      setPagination(response.data.pagination);
    } catch (error) {
      console.error('Error loading trips:', error);
      
      // Better error handling
      if (error.code === 'ERR_NETWORK') {
        toast.error('Network error. Please check your connection.');
      } else if (error.response?.status === 429) {
        toast.error('Too many requests. Please wait a moment.');
      } else {
        toast.error(error.response?.data?.message || 'Failed to load trips');
      }
    } finally {
      setLoading(false);
    }
  }, [pagination.itemsPerPage, filters]);

  // Load active assignments for filter dropdown
  const loadActiveAssignments = useCallback(async () => {
    try {
      const response = await assignmentsAPI.getActive();
      setActiveAssignments(response.data.data || []);
    } catch (error) {
      console.error('Error loading active assignments:', error);
    }
  }, []);

  // Remove debounced function - using proven pattern from Drivers/Trucks pages

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0) {
      const interval = setInterval(() => {
        loadTrips(pagination.currentPage);
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, loadTrips, pagination.currentPage]);

  // Initial load
  useEffect(() => {
    loadTrips();
    loadActiveAssignments();
  }, [loadTrips, loadActiveAssignments]);

  // Handle page change
  const handlePageChange = (page) => {
    loadTrips(page);
  };

  // Handle filter changes
  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  // Handle sorting
  const handleSort = (column) => {
    const newSortOrder = filters.sortBy === column && filters.sortOrder === 'asc' ? 'desc' : 'asc';
    handleFilterChange({
      sortBy: column,
      sortOrder: newSortOrder
    });
  };

  // Handle search - use proven pattern from Drivers/Trucks pages
  const handleSearch = (searchTerm) => {
    setFilters(prev => ({ ...prev, search: searchTerm }));
  };

  // Clear all filters - use proven pattern
  const clearFilters = () => {
    setFilters({
      search: '',
      status: '',
      assignment_id: '',
      date_from: '',
      date_to: '',
      sortBy: 'created_at',
      sortOrder: 'desc'
    });
  };

  // Handle trip detail view
  const handleViewTrip = (trip) => {
    setSelectedTrip(trip);
    setShowTripDetailModal(true);
  };

  // Handle manual refresh
  const handleRefresh = () => {
    loadTrips(pagination.currentPage);
    toast.success('Trips refreshed');
  };

  // Count active filters
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => 
    key !== 'sortBy' && key !== 'sortOrder' && value !== ''
  ).length;

  // Enhanced trip statistics with better exception handling
  const tripStats = useMemo(() => {
    const stats = {
      total: trips.length,
      loading: trips.filter(t => ['loading_start', 'loading_end'].includes(t.status) && !t.is_exception).length,
      traveling: trips.filter(t => ['unloading_start'].includes(t.status) && !t.is_exception).length,
      unloading: trips.filter(t => ['unloading_end'].includes(t.status) && !t.is_exception).length,
      completed: trips.filter(t => t.status === 'trip_completed').length,
      exceptions: trips.filter(t => t.is_exception && t.status === 'exception_pending').length,
      approvedExceptions: trips.filter(t => t.is_exception && t.exception_approved_at && t.status !== 'exception_pending').length,
      cancelled: trips.filter(t => t.status === 'cancelled').length,
      // Include exception trips in active count when they're progressing
      activeExceptions: trips.filter(t => t.is_exception && ['loading_start', 'loading_end', 'unloading_start', 'unloading_end'].includes(t.status)).length
    };
    stats.active = stats.loading + stats.traveling + stats.unloading + stats.activeExceptions;
    return stats;
  }, [trips]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-900">Trip Monitoring</h1>
          <p className="text-secondary-600 mt-1">Real-time tracking of hauling trips</p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center space-x-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-secondary-700">Auto-refresh</span>
            </label>
            {autoRefresh && (
              <select
                value={refreshInterval}
                onChange={(e) => setRefreshInterval(parseInt(e.target.value))}
                className="text-sm border-secondary-300 rounded-md"
              >
                <option value={10}>10s</option>
                <option value={30}>30s</option>
                <option value={60}>1m</option>
                <option value={300}>5m</option>
              </select>
            )}
          </div>
          
          <button
            onClick={handleRefresh}
            className="btn btn-secondary"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh
          </button>
        </div>
      </div>

      {/* Trip Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-medium text-sm">{tripStats.active}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Active</p>
              <p className="text-xs text-secondary-500">In progress</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{tripStats.loading}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Loading</p>
              <p className="text-xs text-secondary-500">At source</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600 font-medium text-sm">{tripStats.traveling}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Traveling</p>
              <p className="text-xs text-secondary-500">En route</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 font-medium text-sm">{tripStats.unloading}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Unloading</p>
              <p className="text-xs text-secondary-500">At destination</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 font-medium text-sm">{tripStats.completed}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Completed</p>
              <p className="text-xs text-secondary-500">Finished</p>
            </div>
          </div>
        </div>        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <span className="text-red-600 font-medium text-sm">{tripStats.exceptions}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Exceptions</p>
              <p className="text-xs text-secondary-500">Need attention</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                <span className="text-amber-600 font-medium text-sm">{tripStats.approvedExceptions}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Approved Exceptions</p>
              <p className="text-xs text-secondary-500">Revised routes</p>
            </div>
          </div>
        </div>

        {/* Cancelled Trips Card */}
        <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-gray-600 font-medium text-sm">{tripStats.cancelled}</span>
              </div>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-secondary-900">Cancelled</p>
              <p className="text-xs text-secondary-500">Rejected exceptions</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-4 rounded-lg shadow-sm border border-secondary-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* Search */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-secondary-700 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Search trips..."
              className="input"
            />
          </div>

          {/* Status Filter */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-secondary-700 mb-1">
              Status
            </label>
            <select
              id="status"
              value={filters.status}
              onChange={(e) => handleFilterChange({ status: e.target.value })}
              className="input"
            >
              <option value="">All Status</option>
              <option value="assigned">Assigned</option>
              <option value="loading_start">Loading Started</option>
              <option value="loading_end">Loading Completed</option>
              <option value="unloading_start">Unloading Started</option>
              <option value="unloading_end">Unloading Completed</option>
              <option value="trip_completed">Trip Completed</option>
              <option value="exception_pending">Exception Pending</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          {/* Assignment Filter */}
          <div>
            <label htmlFor="assignment_id" className="block text-sm font-medium text-secondary-700 mb-1">
              Assignment
            </label>
            <select
              id="assignment_id"
              value={filters.assignment_id}
              onChange={(e) => handleFilterChange({ assignment_id: e.target.value })}
              className="input"
            >
              <option value="">All Assignments</option>
              {activeAssignments.map((assignment) => (
                <option key={assignment.id} value={assignment.id}>
                  {assignment.truck_number} - {assignment.driver_name}
                </option>
              ))}
            </select>
          </div>

          {/* Date From */}
          <div>
            <label htmlFor="date_from" className="block text-sm font-medium text-secondary-700 mb-1">
              From Date
            </label>
            <input
              type="date"
              id="date_from"
              value={filters.date_from}
              onChange={(e) => handleFilterChange({ date_from: e.target.value })}
              className="input"
            />
          </div>

          {/* Clear Filters */}
          <div className="flex items-end">
            <button
              onClick={clearFilters}
              disabled={activeFiltersCount === 0}
              className="btn btn-secondary w-full disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Clear Filters
              {activeFiltersCount > 0 && (
                <span className="ml-1 bg-primary-100 text-primary-800 text-xs rounded-full px-2 py-0.5">
                  {activeFiltersCount}
                </span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Trips Table */}
      <TripsTable
        trips={trips}
        loading={loading}
        pagination={pagination}
        filters={filters}
        onPageChange={handlePageChange}
        onSort={handleSort}
        onViewTrip={handleViewTrip}
      />

      {/* Trip Detail Modal */}
      {showTripDetailModal && selectedTrip && (
        <TripDetailModal
          trip={selectedTrip}
          onClose={() => {
            setShowTripDetailModal(false);
            setSelectedTrip(null);
          }}
          onRefresh={() => loadTrips(pagination.currentPage)}
        />
      )}
    </div>
  );
};

export default TripMonitoring;