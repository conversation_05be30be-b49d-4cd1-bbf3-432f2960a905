#!/usr/bin/env node

const { getClient } = require('../server/config/database');

async function checkDatabaseState() {
  const client = await getClient();
  
  try {
    console.log('🗄️  Checking Database State...\n');

    // Check trip status distribution
    const tripStatusResult = await client.query(`
      SELECT status, COUNT(*) as count 
      FROM trip_logs 
      GROUP BY status 
      ORDER BY count DESC
    `);
    
    console.log('📊 Trip Status Distribution:');
    tripStatusResult.rows.forEach(row => {
      console.log(`   ${row.status}: ${row.count} trips`);
    });

    // Check for exception states
    const exceptionResult = await client.query(`
      SELECT COUNT(*) as count
      FROM trip_logs
      WHERE status::text LIKE '%exception%'
    `);
    
    console.log(`\n🚨 Exception States: ${exceptionResult.rows[0].count} trips`);

    // Check assignment status distribution
    const assignmentResult = await client.query(`
      SELECT status, COUNT(*) as count 
      FROM assignments 
      GROUP BY status 
      ORDER BY count DESC
    `);
    
    console.log('\n📋 Assignment Status Distribution:');
    assignmentResult.rows.forEach(row => {
      console.log(`   ${row.status}: ${row.count} assignments`);
    });

    // Check data integrity
    const integrityResult = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM trip_logs WHERE assignment_id IS NULL) as trips_without_assignments,
        (SELECT COUNT(*) FROM assignments WHERE truck_id IS NULL) as assignments_without_trucks,
        (SELECT COUNT(*) FROM trip_logs WHERE status = 'trip_completed') as completed_trips
    `);
    
    console.log('\n🔍 Data Integrity Check:');
    const integrity = integrityResult.rows[0];
    console.log(`   Trips without assignments: ${integrity.trips_without_assignments}`);
    console.log(`   Assignments without trucks: ${integrity.assignments_without_trucks}`);
    console.log(`   Completed trips: ${integrity.completed_trips}`);

    // Summary
    console.log('\n✅ Database State Summary:');
    console.log(`   - Exception states: ${exceptionResult.rows[0].count === '0' ? '✅ Clean' : '⚠️  Found'}`);
    console.log(`   - Data integrity: ${integrity.trips_without_assignments === '0' && integrity.assignments_without_trucks === '0' ? '✅ Good' : '⚠️  Issues'}`);
    console.log(`   - Trip completion: ${integrity.completed_trips > 0 ? '✅ Working' : '⚠️  No completed trips'}`);

  } catch (error) {
    console.error('❌ Database check failed:', error);
  } finally {
    await client.release();
  }
}

if (require.main === module) {
  checkDatabaseState()
    .then(() => {
      console.log('\n🎯 Database state check completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Database state check failed:', error);
      process.exit(1);
    });
}

module.exports = { checkDatabaseState };
