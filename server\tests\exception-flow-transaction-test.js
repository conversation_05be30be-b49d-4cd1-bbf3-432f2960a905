/**
 * Exception Flow Transaction Test Script
 * 
 * This test script verifies that the constraint violation fix works correctly
 * by simulating multiple concurrent trip assignment updates and ensuring that
 * no constraint violations occur.
 * 
 * Run this with: node server/tests/exception-flow-transaction-test.js
 */

const { getClient } = require('../config/database');
const exceptionFlowManager = require('../utils/exception-flow-manager');

// Utility function for waiting
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Run a test with multiple concurrent assignment attempts
 */
async function runConcurrencyTest() {
  console.log('====================================');
  console.log('STARTING EXCEPTION FLOW CONCURRENCY TEST');
  console.log('====================================');
  console.log('This test verifies that the constraint violation fix properly');
  console.log('handles concurrent trip reassignment operations without errors.');
  console.log('====================================\n');

  const client = await getClient();
  
  try {
    // First, let's verify we have the required test data
    console.log('STEP 0: Verifying test data availability...');
    const dataCheckResult = await client.query(`
      SELECT 
        (SELECT COUNT(*) FROM dump_trucks WHERE status = 'active') as active_trucks,
        (SELECT COUNT(*) FROM drivers WHERE status = 'active') as active_drivers,
        (SELECT COUNT(*) FROM locations WHERE type = 'loading') as loading_locations,
        (SELECT COUNT(*) FROM locations WHERE type = 'unloading') as unloading_locations
    `);
    
    const dataCheck = dataCheckResult.rows[0];
    console.log(`Available test data: ${dataCheck.active_trucks} trucks, ${dataCheck.active_drivers} drivers, ${dataCheck.loading_locations} loading locations, ${dataCheck.unloading_locations} unloading locations`);
    
    if (dataCheck.active_trucks < 1 || dataCheck.active_drivers < 1 || 
        dataCheck.loading_locations < 1 || dataCheck.unloading_locations < 1) {
      console.log('⚠️ Insufficient test data detected. Creating minimal test data...');
      
      // Create minimal test data
      if (dataCheck.active_trucks < 1) {
        await client.query(`
          INSERT INTO dump_trucks (truck_number, capacity_tons, status, license_plate)
          VALUES ('TEST-TRUCK-001', 25, 'active', 'TEST001')
          ON CONFLICT (truck_number) DO NOTHING
        `);
      }
      
      if (dataCheck.active_drivers < 1) {
        await client.query(`
          INSERT INTO drivers (full_name, license_number, status)
          VALUES ('Test Driver', 'TEST123456', 'active')
          ON CONFLICT (license_number) DO NOTHING
        `);
      }
      
      if (dataCheck.loading_locations < 1) {
        await client.query(`
          INSERT INTO locations (name, type, address, status)
          VALUES ('Test Loading Site', 'loading', '123 Test Street', 'active')
          ON CONFLICT (name) DO NOTHING
        `);
      }
      
      if (dataCheck.unloading_locations < 1) {
        await client.query(`
          INSERT INTO locations (name, type, address, status)
          VALUES ('Test Unloading Site', 'unloading', '456 Test Avenue', 'active')
          ON CONFLICT (name) DO NOTHING
        `);
      }
      
      console.log('✅ Created minimal test data');
    }

    // Step 1: Create a test assignment
    console.log('STEP 1: Creating test assignment...');
    const assignmentResult = await client.query(`
      INSERT INTO assignments (
        assignment_code, truck_id, driver_id,
        loading_location_id, unloading_location_id, 
        status, priority, assigned_date
      )
      SELECT 
        'TEST-' || floor(random() * 1000000)::text,
        dt.id,
        d.id,
        ll.id,
        ul.id,
        'assigned',
        'normal',
        CURRENT_DATE
      FROM 
        dump_trucks dt,
        drivers d,
        locations ll,
        locations ul
      WHERE 
        dt.status = 'active' AND
        d.status = 'active' AND
        ll.type = 'loading' AND
        ul.type = 'unloading'
      LIMIT 1
      RETURNING id, assignment_code, truck_id, loading_location_id, unloading_location_id
    `);

    if (assignmentResult.rows.length === 0) {
      throw new Error('Failed to create test assignment');
    }
    
    const testAssignment = assignmentResult.rows[0];
    console.log(`Created test assignment with ID ${testAssignment.id} and code ${testAssignment.assignment_code}`);

    // Step 2: Create 5 test trip logs
    console.log('\nSTEP 2: Creating test trip logs...');
    const tripLogs = [];
    
    for (let i = 0; i < 5; i++) {
      // Create a different assignment for each trip to test reassignment
      const otherAssignmentResult = await client.query(`
        INSERT INTO assignments (
          assignment_code, truck_id, driver_id,
          loading_location_id, unloading_location_id, 
          status, priority, assigned_date
        )
        SELECT 
          'TEST-OTHER-' || floor(random() * 1000000)::text,
          dt.id,
          d.id,
          ll.id,
          ul.id,
          'assigned',
          'normal',
          CURRENT_DATE
        FROM 
          dump_trucks dt,
          drivers d,
          locations ll,
          locations ul
        WHERE 
          dt.status = 'active' AND
          d.status = 'active' AND
          ll.type = 'loading' AND
          ul.type = 'unloading'
        LIMIT 1
        RETURNING id, assignment_code
      `);
      
      const otherAssignment = otherAssignmentResult.rows[0];
      
      // Create trip log on this other assignment
      const tripResult = await client.query(`
        INSERT INTO trip_logs (
          assignment_id, trip_number, status, is_exception, created_at
        )
        VALUES ($1, 1, 'exception_pending', true, CURRENT_TIMESTAMP)
        RETURNING id
      `, [otherAssignment.id]);
      
      tripLogs.push({
        id: tripResult.rows[0].id,
        originalAssignmentId: otherAssignment.id
      });
      
      console.log(`Created test trip log ${i+1} with ID ${tripResult.rows[0].id} on assignment ${otherAssignment.id}`);
    }

    // Step 3: Run concurrent updates
    console.log('\nSTEP 3: Running concurrent updates to test constraint handling...');
    const promises = tripLogs.map(async (trip, index) => {
      // Add slight delays to simulate real-world concurrency patterns
      await sleep(index * 50);
      
      try {
        // Use a separate client for each transaction to simulate concurrent connections
        const concurrentClient = await getClient();
        
        try {
          await concurrentClient.query('BEGIN');
          
          console.log(`Starting update for trip ${trip.id} (${index+1}/5)...`);
          
          // Use the verified function with the target assignment
          const feasibilityCheck = await exceptionFlowManager.verifyAssignmentUpdateFeasibility(
            concurrentClient, trip.id, testAssignment
          );
          
          console.log(`Feasibility check for trip ${trip.id}: ${feasibilityCheck.feasible ? 'PASSED' : 'FAILED'}`);
          
          if (feasibilityCheck.feasible) {
            await exceptionFlowManager.updateTripToUseExistingAssignment(
              concurrentClient, trip.id, testAssignment, 1, 
              { suggestedTripNumber: feasibilityCheck.suggestedTripNumber }
            );
            
            // Verify the update succeeded
            const verifyResult = await concurrentClient.query(`
              SELECT assignment_id, trip_number FROM trip_logs WHERE id = $1
            `, [trip.id]);
            
            if (verifyResult.rows[0].assignment_id === testAssignment.id) {
              console.log(`✅ Trip ${trip.id} successfully updated to assignment ${testAssignment.id} with trip number ${verifyResult.rows[0].trip_number}`);
            } else {
              console.log(`❌ Failed to update trip ${trip.id}`);
            }
          } else {
            console.log(`⚠️ Cannot update trip ${trip.id}: ${feasibilityCheck.reason}`);
          }
          
          await concurrentClient.query('COMMIT');
          
        } catch (transactionError) {
          // Rollback on any error
          try {
            await concurrentClient.query('ROLLBACK');
          } catch (rollbackError) {
            console.warn(`Rollback failed for trip ${trip.id}:`, rollbackError.message);
          }
          throw transactionError; // Re-throw original error
        } finally {
          concurrentClient.release();
        }
        
      } catch (error) {
        console.error(`❌ ERROR updating trip ${trip.id}:`, error.message);
        return {
          success: false,
          tripId: trip.id,
          error: error.message
        };
      }
      
      return { 
        success: true,
        tripId: trip.id
      };
    });
    
    const results = await Promise.all(promises);
    
    // Step 4: Verify results
    console.log('\nSTEP 4: Verifying results...');
    
    const successCount = results.filter(r => r.success).length;
    console.log(`${successCount} of ${results.length} updates completed successfully`);
    
    // Check all trips now have unique trip numbers
    const verifyResult = await client.query(`
      SELECT 
        id, trip_number, 
        COUNT(*) OVER (PARTITION BY assignment_id, trip_number) as duplicate_count
      FROM trip_logs
      WHERE assignment_id = $1
    `, [testAssignment.id]);
    
    const duplicates = verifyResult.rows.filter(row => row.duplicate_count > 1);
    
    if (duplicates.length > 0) {
      console.error('❌ TEST FAILED: Found duplicate trip numbers!');
      console.error('Duplicates:', duplicates);
    } else {
      console.log('✅ TEST PASSED: No duplicate trip numbers found');
      console.log('All trips have unique (assignment_id, trip_number) combinations');
    }
    
    // Step 5: Clean up
    console.log('\nSTEP 5: Cleaning up test data...');
    
    try {
      // Delete the test trip logs first (to avoid foreign key constraints)
      if (tripLogs.length > 0) {
        const tripIds = tripLogs.map(t => t.id).join(', ');
        await client.query(`DELETE FROM trip_logs WHERE id IN (${tripIds})`);
        console.log(`Deleted ${tripLogs.length} test trip logs`);
      }
      
      // Delete all test assignments
      const deletedAssignments = await client.query(`
        DELETE FROM assignments 
        WHERE assignment_code LIKE 'TEST-%'
        RETURNING id, assignment_code
      `);
      
      console.log(`Deleted ${deletedAssignments.rows.length} test assignments`);
      console.log('Cleanup completed successfully');
      
    } catch (cleanupError) {
      console.warn('Cleanup warning (non-critical):', cleanupError.message);
    }

  } catch (error) {
    console.error('Test error:', error);
  } finally {
    client.release();
    console.log('\n====================================');
    console.log('TEST COMPLETED');
    console.log('====================================');
  }
}

// Run the test
runConcurrencyTest().catch(console.error);
