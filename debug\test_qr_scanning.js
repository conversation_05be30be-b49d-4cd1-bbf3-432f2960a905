#!/usr/bin/env node

/**
 * QR Scanning Test Script
 * 
 * This script tests the QR scanning functionality to verify the assignmentValidator fix.
 */

const axios = require('axios');

async function testQRScanning() {
  try {
    console.log('📱 Testing QR Scanning Functionality...\n');

    const baseURL = 'http://localhost:5000/api';
    
    // Test data
    const locationScanData = {
      id: "LOC-001",
      name: "Point A - Main Loading Site",
      type: "location",
      timestamp: "2025-06-27T08:19:18.248Z",
      coordinates: null
    };

    const truckScanData = {
      id: "DT-100",
      type: "truck",
      driver_id: "DR-001",
      timestamp: "2025-01-01T00:00:00Z",
      assigned_route: "A-B"
    };

    // Test authentication token (you may need to update this)
    const authToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************.Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7Ej7E";

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${authToken}`
    };

    // Test 1: Location Scan
    console.log('🔍 Test 1: Location Scan');
    console.log('=' .repeat(50));
    
    try {
      const locationResponse = await axios.post(`${baseURL}/scanner/scan`, {
        scan_type: "location",
        scanned_data: JSON.stringify(locationScanData)
      }, { headers });

      console.log('✅ Location scan successful');
      console.log(`📊 Response: ${locationResponse.data.message}`);
      console.log(`📍 Location: ${locationResponse.data.data.location.name}`);
      console.log(`🎯 Next step: ${locationResponse.data.next_step}`);
      
    } catch (error) {
      console.log('❌ Location scan failed:', error.response?.data?.message || error.message);
      return;
    }

    // Test 2: Truck Scan (This should test the assignmentValidator fix)
    console.log('\n\n🚛 Test 2: Truck Scan (Testing assignmentValidator fix)');
    console.log('=' .repeat(50));
    
    try {
      const truckResponse = await axios.post(`${baseURL}/scanner/scan`, {
        scan_type: "truck",
        scanned_data: JSON.stringify(truckScanData),
        location_scan_data: locationScanData
      }, { headers });

      console.log('✅ Truck scan successful - assignmentValidator fix working!');
      console.log(`📊 Response: ${truckResponse.data.message}`);
      
      if (truckResponse.data.data) {
        console.log(`🚛 Truck: ${truckResponse.data.data.truck?.truck_number || 'Unknown'}`);
        console.log(`📋 Assignment: ${truckResponse.data.data.assignment?.assignment_code || 'Unknown'}`);
        console.log(`🛣️  Trip: ${truckResponse.data.data.trip?.trip_number || 'Unknown'}`);
        console.log(`📍 Status: ${truckResponse.data.data.trip?.status || 'Unknown'}`);
      }
      
    } catch (error) {
      console.log('❌ Truck scan failed:', error.response?.data?.message || error.message);
      
      if (error.response?.data?.error?.includes('assignmentValidator')) {
        console.log('🔧 This indicates the assignmentValidator fix needs to be applied');
      }
      
      console.log('📋 Full error details:', error.response?.data || error.message);
    }

    // Test 3: Check Server Logs for Errors
    console.log('\n\n📋 Test 3: Server Status Check');
    console.log('=' .repeat(50));
    
    try {
      // Try to ping the server
      const healthResponse = await axios.get(`${baseURL}/health`, { headers });
      console.log('✅ Server is responding');
      console.log(`📊 Status: ${healthResponse.status}`);
    } catch (error) {
      console.log('❌ Server health check failed:', error.message);
    }

    console.log('\n🎯 QR Scanning Test Summary');
    console.log('=' .repeat(50));
    console.log('✅ Location scan functionality tested');
    console.log('✅ Truck scan functionality tested');
    console.log('✅ assignmentValidator fix verification attempted');
    console.log('\n📝 Note: If truck scan succeeded, the assignmentValidator fix is working correctly!');

  } catch (error) {
    console.error('❌ QR scanning test failed:', error.message);
  }
}

// Run test if called directly
if (require.main === module) {
  testQRScanning()
    .then(() => {
      console.log('\n✅ QR scanning test completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ QR scanning test failed:', error);
      process.exit(1);
    });
}

module.exports = { testQRScanning };
