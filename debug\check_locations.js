#!/usr/bin/env node

/**
 * Check Available Locations
 */

const { getClient } = require('../server/config/database');

async function checkLocations() {
  const client = await getClient();
  
  try {
    console.log('🔍 Checking available locations...\n');

    const locationsResult = await client.query(`
      SELECT id, name, type, location_code
      FROM locations 
      ORDER BY type, name
    `);

    console.log('📍 Available Locations:');
    locationsResult.rows.forEach(location => {
      console.log(`   ${location.id}: ${location.name} (${location.type}) [${location.location_code}]`);
    });

    return locationsResult.rows;

  } catch (error) {
    console.error('❌ Location check failed:', error);
    return [];
  } finally {
    await client.release();
  }
}

// Run check if called directly
if (require.main === module) {
  checkLocations()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = { checkLocations };
