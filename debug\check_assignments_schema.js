#!/usr/bin/env node

/**
 * Check Assignments Table Schema
 */

const { getClient } = require('../server/config/database');

async function checkAssignmentsSchema() {
  const client = await getClient();
  
  try {
    console.log('🔍 Checking assignments table schema...\n');

    // Check table structure
    const schemaResult = await client.query(`
      SELECT column_name, is_nullable, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'assignments' AND table_schema = 'public' 
      ORDER BY ordinal_position
    `);

    console.log('📊 Assignments Table Schema:');
    schemaResult.rows.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable}) default: ${col.column_default || 'none'}`);
    });

    // Check existing assignments
    const assignmentsResult = await client.query(`
      SELECT id, assignment_code, loading_location_id, unloading_location_id, status, notes
      FROM assignments 
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    console.log('\n📋 Recent Assignments:');
    assignmentsResult.rows.forEach(assignment => {
      console.log(`   ${assignment.assignment_code}: ${assignment.loading_location_id} → ${assignment.unloading_location_id} (${assignment.status})`);
    });

    return true;

  } catch (error) {
    console.error('❌ Schema check failed:', error);
    return false;
  } finally {
    await client.release();
  }
}

// Run check if called directly
if (require.main === module) {
  checkAssignmentsSchema()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}

module.exports = { checkAssignmentsSchema };
